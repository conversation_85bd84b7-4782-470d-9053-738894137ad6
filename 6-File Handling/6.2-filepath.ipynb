{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### Working With File Paths\n", "When working with files in Python, handling file paths correctly is crucial to ensure your code works across different operating systems and environments. Python provides several modules and functions for working with file paths effectively."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Current working directory is e:\\UDemy Final\\python\\6-File Handling\n"]}], "source": ["#### Using the os module\n", "import os\n", "cwd=os.getcwd()\n", "print(f\"Current working directory is {cwd}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Directory 'package' create\n"]}], "source": ["## create a new directory\n", "new_directory=\"package\"\n", "os.mkdir(new_directory)\n", "print(f\"Directory '{new_directory}' create\")\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['6.1-fileoperation.ipynb', '6.2-filepath.ipynb', 'destination.txt', 'example.bin', 'example.txt', 'package']\n"]}], "source": ["## Listing Files And Directories\n", "items=os.listdir('.')\n", "print(items)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["folder\\file.txt\n"]}], "source": ["### Joining Paths\n", "\n", "dir_name=\"folder\"\n", "file_name=\"file.txt\"\n", "full_path=os.path.join(dir_name,file_name)\n", "print(full_path)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["e:\\UDemy Final\\python\\6-File Handling\\folder\\file.txt\n"]}], "source": ["\n", "dir_name=\"folder\"\n", "file_name=\"file.txt\"\n", "full_path=os.path.join(os.getcwd(),dir_name,file_name)\n", "print(full_path)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The path 'example1.txt' does not exists\n"]}], "source": ["path='example1.txt'\n", "if os.path.exists(path):\n", "    print(f\"The path '{path}' exists\")\n", "else:\n", "    print(f\"The path '{path}' does not exists\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The path 'example.txt' is a file.\n"]}], "source": ["#Checking if a Path is a File or Directory\n", "import os\n", "\n", "path = 'example.txt'\n", "if os.path.isfile(path):\n", "    print(f\"The path '{path}' is a file.\")\n", "elif os.path.isdir(path):\n", "    print(f\"The path '{path}' is a directory.\")\n", "else:\n", "    print(f\"The path '{path}' is neither a file nor a directory.\")\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["e:\\UDemy Final\\python\\6-File Handling\\example.txt\n"]}], "source": ["## Getting the absolute path\n", "relative_path='example.txt'\n", "absolute_path=os.path.abspath(relative_path)\n", "print(absolute_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}