{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### File Operation- Read And Write Files\n", "\n", "File handling is a crucial part of any programming language. Python provides built-in functions and methods to read from and write to files, both text and binary. This lesson will cover the basics of file handling, including reading and writing text files and binary files."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello How are you?\n", "I am good\n", "<PERSON><PERSON> is my name\n", "Welcome to the course\n"]}], "source": ["### Read a Whole File\n", "\n", "with open('example.txt','r') as file:\n", "    content=file.read()\n", "    print(content)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello How are you?\n", "I am good\n", "<PERSON><PERSON> is my name\n", "Welcome to the course\n"]}], "source": ["## Read a file line by line\n", "with open('example.txt','r') as file:\n", "    for line in file:\n", "        print(line.strip()) ## sstrip() removes the newline character"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["## Writing a file(Overwriting)\n", "\n", "with open('example.txt','w') as file:\n", "    file.write('Hello World!\\n')\n", "    file.write('this is a new line.')"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["## Write a file(wwithout Overwriting)\n", "with open('example.txt','a') as file:\n", "    file.write(\"Append operation taking place!\\n\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["### Writing a list of lines to a file\n", "lines=['First line \\n','Second line \\n','Third line\\n']\n", "with open('example.txt','a') as file:\n", "    file.writelines(lines)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["### Binary Files\n", "\n", "# Writing to a binary file\n", "data = b'\\x00\\x01\\x02\\x03\\x04'\n", "with open('example.bin', 'wb') as file:\n", "    file.write(data)\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["b'\\x00\\x01\\x02\\x03\\x04'\n"]}], "source": ["# Reading a binary file\n", "with open('example.bin', 'rb') as file:\n", "    content = file.read()\n", "    print(content)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["### Read the content froma  source text fiile and write to a destination text file\n", "# Copying a text file\n", "with open('example.txt', 'r') as source_file:\n", "    content = source_file.read()\n", "\n", "with open('destination.txt', 'w') as destination_file:\n", "    destination_file.write(content)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#Read a text file and count the number of lines, words, and characters.\n", "# Counting lines, words, and characters in a text file\n", "def count_text_file(file_path):\n", "    with open(file_path, 'r') as file:\n", "        lines = file.readlines()\n", "        line_count = len(lines)\n", "        word_count = sum(len(line.split()) for line in lines)\n", "        char_count = sum(len(line) for line in lines)\n", "    return line_count, word_count, char_count\n", "\n", "file_path = 'example.txt'\n", "lines, words, characters = count_text_file(file_path)\n", "print(f'Lines: {lines}, Words: {words}, Characters: {characters}')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The w+ mode in Python is used to open a file for both reading and writing. If the file does not exist, it will be created. If the file exists, its content is truncated (i.e., the file is overwritten)."]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello world\n", "This is a new line \n", "\n"]}], "source": ["### Writing and then reading a file\n", "\n", "with open('example.txt','w+') as file:\n", "    file.write(\"Hello world\\n\")\n", "    file.write(\"This is a new line \\n\")\n", "\n", "    ## Move the file cursor to the beginning\n", "    file.seek(0)\n", "\n", "    ## Read the content of the file\n", "    content=file.read()\n", "    print(content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}