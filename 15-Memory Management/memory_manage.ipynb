{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### Python Memory Management\n", "Memory management in Python involves a combination of automatic garbage collection, reference counting, and various internal optimizations to efficiently manage memory allocation and deallocation. Understanding these mechanisms can help developers write more efficient and robust applications.\n", "\n", "1. Key Concepts in Python Memory Management\n", "2. Memory Allocation and Deallocation\n", "3. Reference Counting\n", "4. Garbage Collection\n", "5. The gc Module\n", "6. Memory Management Best Practices"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Reference Counting\n", "Reference counting is the primary method Python uses to manage memory. Each object in Python maintains a count of references pointing to it. When the reference count drops to zero, the memory occupied by the object is deallocated."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2\n"]}], "source": ["import sys\n", "\n", "a=[]\n", "## 2 (one reference from 'a' and one from getrefcount())\n", "print(sys.getrefcount(a))"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3\n"]}], "source": ["b=a\n", "print(sys.getrefcount(b))"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2\n"]}], "source": ["del b\n", "print(sys.getrefcount(a))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Garbage Collection\n", "Python includes a cyclic garbage collector to handle reference cycles. Reference cycles occur when objects reference each other, preventing their reference counts from reaching zero."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import gc\n", "## enable garbage collection\n", "gc.enable()\n", "\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["gc.disable()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["1036"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["gc.collect()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'collections': 182, 'collected': 1611, 'uncollectable': 0}, {'collections': 16, 'collected': 255, 'uncollectable': 0}, {'collections': 2, 'collected': 1036, 'uncollectable': 0}]\n"]}], "source": ["### Get garbage collection stats\n", "print(gc.get_stats())"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[]\n"]}], "source": ["### get unreachable objects\n", "print(gc.garbage)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["####  Memory Management Best Practices\n", "1. Use Local Variables: Local variables have a shorter lifespan and are freed sooner than global variables.\n", "2. Avoid Circular References: Circular references can lead to memory leaks if not properly managed.\n", "3. Use Generators: Generators produce items one at a time and only keep one item in memory at a time, making them memory efficient.\n", "4. Explicitly Delete Objects: Use the del statement to delete variables and objects explicitly.\n", "5. Profile Memory Usage: Use memory profiling tools like tracemalloc and memory_profiler to identify memory leaks and optimize memory usage."]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Object obj1 created\n", "Object obj2 created\n", "Object obj1 deleted\n", "Object obj2 deleted\n", "Object obj1 deleted\n", "Object obj2 deleted\n", "Object obj1 deleted\n", "Object obj2 deleted\n"]}, {"name": "stderr", "output_type": "stream", "text": ["gc: collectable <NameError 0x00000205D63FEF80>\n", "gc: collectable <traceback 0x00000205D69F5D80>\n", "gc: collectable <traceback 0x00000205D69F7980>\n", "gc: collectable <frame 0x00000205D646D240>\n", "gc: collectable <function 0x00000205D64FFD80>\n", "gc: collectable <frame 0x00000205D6453230>\n", "gc: collectable <frame 0x00000205D6A10CA0>\n", "gc: collectable <ExecutionResult 0x00000205D647D7F0>\n", "gc: collectable <frame 0x00000205D64B07C0>\n", "gc: collectable <list 0x00000205D6A03A00>\n", "gc: collectable <list 0x00000205D6A00640>\n", "gc: collectable <list 0x00000205D6A1C380>\n", "gc: collectable <function 0x00000205D642BA60>\n", "gc: collectable <list 0x00000205D6A03B80>\n", "gc: collectable <Delete 0x00000205D69F6B90>\n", "gc: collectable <Module 0x00000205D50EF310>\n", "gc: collectable <ExecutionInfo 0x00000205D647E2D0>\n", "gc: collectable <frame 0x00000205D629FC40>\n", "gc: collectable <cell 0x00000205D647CC10>\n", "gc: collectable <cell 0x00000205D647E3E0>\n", "gc: collectable <function 0x00000205D64FFE20>\n", "gc: collectable <Module 0x00000205D646B510>\n", "gc: collectable <cell 0x00000205D647D930>\n", "gc: collectable <Expr 0x00000205D69F4B90>\n", "gc: collectable <tuple 0x00000205D6A242C0>\n", "gc: collectable <tuple 0x00000205D6A1F540>\n", "gc: collectable <dict 0x00000205D69F5940>\n", "gc: collectable <dict 0x00000205D6468B40>\n", "gc: collectable <frame 0x00000205D6251BC0>\n", "gc: collectable <coroutine 0x00000205D6941F80>\n", "gc: collectable <tuple 0x00000205D6A1D280>\n", "gc: collectable <dict 0x00000205D69F5640>\n", "gc: collectable <dict 0x00000205D69F5C00>\n", "gc: collectable <list 0x00000205D69CAF40>\n", "gc: collectable <list 0x00000205D6A1C180>\n", "gc: collectable <list 0x00000205D6A02FC0>\n", "gc: collectable <frame 0x00000205D641EA40>\n", "gc: collectable <list 0x00000205D6A0D040>\n", "gc: collectable <Call 0x00000205D69F7690>\n", "gc: collectable <Name 0x00000205D69F4CD0>\n", "gc: collectable <frame 0x00000205D6A41560>\n", "gc: collectable <dict 0x00000205D69F5EC0>\n", "gc: collectable <dict 0x00000205D6994900>\n", "gc: collectable <frame 0x00000205D62458B0>\n", "gc: collectable <Name 0x00000205D69F5150>\n", "gc: collectable <list 0x00000205D6A040C0>\n", "gc: collectable <list 0x00000205D69F7040>\n", "gc: collectable <frame 0x00000205D624CAC0>\n", "gc: collectable <dict 0x00000205D6A0EC00>\n", "gc: collectable <method 0x00000205D69F6740>\n", "gc: collectable <method 0x00000205D69F4A40>\n", "gc: collectable <cell 0x00000205D64A4970>\n", "gc: collectable <dict 0x00000205D69F5D00>\n", "gc: collectable <Call 0x00000205D69F7810>\n", "gc: collectable <frame 0x00000205D64AAEA0>\n", "gc: collectable <list 0x00000205D64A00C0>\n", "gc: collectable <dict 0x00000205D69F6440>\n", "gc: collectable <dict 0x00000205D69F5240>\n", "gc: collectable <dict 0x00000205D69FA140>\n", "gc: collectable <dict 0x00000205D69F5500>\n", "gc: collectable <list 0x00000205D6A06E40>\n", "gc: collectable <list 0x00000205D6A07D40>\n", "gc: collectable <dict 0x00000205D69F5E80>\n", "gc: collectable <frame 0x00000205D6A3C5E0>\n", "gc: collectable <list 0x00000205D64A0680>\n", "gc: collectable <Attribute 0x00000205D69F5110>\n", "gc: collectable <list 0x00000205D6A1EE40>\n", "gc: collectable <list 0x00000205D6A019C0>\n", "gc: collectable <frame 0x00000205D6A42A80>\n", "gc: collectable <coroutine 0x00000205D641FA40>\n", "gc: collectable <dict 0x00000205D69F5E00>\n", "gc: collectable <Name 0x00000205D69F56D0>\n", "gc: collectable <tuple 0x00000205D65C2350>\n", "gc: collectable <Name 0x00000205D69F6590>\n", "gc: collectable <dict 0x00000205D69F4F00>\n", "gc: collectable <list 0x00000205D6488E80>\n", "gc: collectable <dict 0x00000205D69F5BC0>\n", "gc: collectable <Frame 0x00000205D62D9020>\n", "gc: collectable <Frame 0x00000205D62D8F70>\n", "gc: collectable <Frame 0x00000205D62D8EC0>\n", "gc: collectable <Frame 0x00000205D62D8E10>\n", "gc: collectable <Frame 0x00000205D62D8D60>\n", "gc: collectable <Frame 0x00000205D62D8C00>\n", "gc: collectable <Frame 0x00000205D62D8CB0>\n", "gc: collectable <cell 0x00000205D65C0D00>\n", "gc: collectable <function 0x00000205D6A607C0>\n", "gc: collectable <StyleMeta 0x00000205D597C500>\n", "gc: collectable <tuple 0x00000205D6B19670>\n", "gc: collectable <Terminal256Formatter 0x00000205D65C2210>\n", "gc: collectable <dict 0x00000205D6B25680>\n", "gc: collectable <list 0x00000205D6B2E0C0>\n", "gc: collectable <dict 0x00000205D6B2A680>\n", "gc: collectable <Options 0x00000205D64A66C0>\n", "gc: collectable <FrameInfo 0x00000205D65EC8C0>\n", "gc: collectable <tuple 0x00000205D65E9510>\n", "gc: collectable <dict 0x00000205D6B25C00>\n", "gc: collectable <tuple 0x00000205D663EDD0>\n", "gc: collectable <dict 0x00000205D6B2B240>\n", "gc: collectable <dict 0x00000205D6B29840>\n", "gc: collectable <list 0x00000205D1048840>\n", "gc: collectable <list 0x00000205D6B296C0>\n", "gc: collectable <list 0x00000205D6B24200>\n", "gc: collectable <list 0x00000205D6B29800>\n", "gc: collectable <list 0x00000205D6A2B340>\n", "gc: collectable <list 0x00000205D6B1EC00>\n", "gc: collectable <list 0x00000205D6B25F40>\n", "gc: collectable <list 0x00000205D6B29180>\n", "gc: collectable <list 0x00000205D6B1C180>\n", "gc: collectable <list 0x00000205D6B28B80>\n", "gc: collectable <list 0x00000205D6B0DB00>\n", "gc: collectable <list 0x00000205D6B1DC80>\n", "gc: collectable <list 0x00000205D6B1C100>\n", "gc: collectable <list 0x00000205D6B27F40>\n", "gc: collectable <list 0x00000205D6B1DE40>\n", "gc: collectable <list 0x00000205D6B1DA40>\n", "gc: collectable <list 0x00000205D6B0DD40>\n", "gc: collectable <list 0x00000205D6B13E80>\n", "gc: collectable <list 0x00000205D6AD1C00>\n", "gc: collectable <list 0x00000205D6B1E140>\n", "gc: collectable <list 0x00000205D6B1F580>\n", "gc: collectable <list 0x00000205D6B262C0>\n", "gc: collectable <list 0x00000205D6B24A00>\n", "gc: collectable <list 0x00000205D6B27E40>\n", "gc: collectable <list 0x00000205D6B299C0>\n", "gc: collectable <list 0x00000205D6B2A740>\n", "gc: collectable <list 0x00000205D6B1FA80>\n", "gc: collectable <list 0x00000205D6B1EF80>\n", "gc: collectable <list 0x00000205D6B12D00>\n", "gc: collectable <list 0x00000205D6B25CC0>\n", "gc: collectable <list 0x00000205D6B2A1C0>\n", "gc: collectable <list 0x00000205D6B1CD00>\n", "gc: collectable <list 0x00000205D6B1CB80>\n", "gc: collectable <list 0x00000205D6B1CDC0>\n", "gc: collectable <list 0x00000205D6B1CE80>\n", "gc: collectable <list 0x00000205D6B1CEC0>\n", "gc: collectable <list 0x00000205D6B1CC40>\n", "gc: collectable <list 0x00000205D6B1CF00>\n", "gc: collectable <list 0x00000205D6B1CF40>\n", "gc: collectable <list 0x00000205D6B1CF80>\n", "gc: collectable <list 0x00000205D6B1CFC0>\n", "gc: collectable <list 0x00000205D6B1D000>\n", "gc: collectable <list 0x00000205D6B1CA80>\n", "gc: collectable <list 0x00000205D6B1D040>\n", "gc: collectable <list 0x00000205D6B1D080>\n", "gc: collectable <list 0x00000205D6B1D0C0>\n", "gc: collectable <list 0x00000205D6B1D200>\n", "gc: collectable <list 0x00000205D6B1D280>\n", "gc: collectable <list 0x00000205D6B1D240>\n", "gc: collectable <list 0x00000205D6B1D1C0>\n", "gc: collectable <list 0x00000205D6B1D180>\n", "gc: collectable <list 0x00000205D6B1D2C0>\n", "gc: collectable <list 0x00000205D6B1FC40>\n", "gc: collectable <list 0x00000205D6B1DB40>\n", "gc: collectable <list 0x00000205D6B1E100>\n", "gc: collectable <list 0x00000205D6B1D500>\n", "gc: collectable <list 0x00000205D6B1E1C0>\n", "gc: collectable <list 0x00000205D6B1DE80>\n", "gc: collectable <list 0x00000205D6B1DF80>\n", "gc: collectable <list 0x00000205D6B1E240>\n", "gc: collectable <list 0x00000205D6B1D300>\n", "gc: collectable <list 0x00000205D6B1D900>\n", "gc: collectable <list 0x00000205D6B1E580>\n", "gc: collectable <list 0x00000205D6B1E400>\n", "gc: collectable <list 0x00000205D6B1E380>\n", "gc: collectable <list 0x00000205D6B1E500>\n", "gc: collectable <list 0x00000205D6B1E600>\n", "gc: collectable <list 0x00000205D6B1E300>\n", "gc: collectable <list 0x00000205D6B1E680>\n", "gc: collectable <list 0x00000205D6B1E6C0>\n", "gc: collectable <list 0x00000205D6B1E780>\n", "gc: collectable <list 0x00000205D6B1E7C0>\n", "gc: collectable <list 0x00000205D6B1E980>\n", "gc: collectable <list 0x00000205D6B1C040>\n", "gc: collectable <list 0x00000205D6B1D4C0>\n", "gc: collectable <list 0x00000205D6B1DFC0>\n", "gc: collectable <list 0x00000205D6B1C2C0>\n", "gc: collectable <list 0x00000205D6B1E740>\n", "gc: collectable <list 0x00000205D6B1E900>\n", "gc: collectable <list 0x00000205D6B1E880>\n", "gc: collectable <list 0x00000205D6B1E700>\n", "gc: collectable <list 0x00000205D6B1DCC0>\n", "gc: collectable <list 0x00000205D6B1ED40>\n", "gc: collectable <list 0x00000205D6B1EB00>\n", "gc: collectable <list 0x00000205D6B1EE80>\n", "gc: collectable <list 0x00000205D6B1EF40>\n", "gc: collectable <list 0x00000205D6B1F100>\n", "gc: collectable <list 0x00000205D6B1F340>\n", "gc: collectable <list 0x00000205D6B1F380>\n", "gc: collectable <list 0x00000205D6B1F440>\n", "gc: collectable <list 0x00000205D6B1E800>\n", "gc: collectable <list 0x00000205D6B1F8C0>\n", "gc: collectable <list 0x00000205D6B1D580>\n", "gc: collectable <list 0x00000205D6B1FEC0>\n", "gc: collectable <list 0x00000205D6B1E840>\n", "gc: collectable <list 0x00000205D6B1ED80>\n", "gc: collectable <list 0x00000205D6B1FBC0>\n", "gc: collectable <list 0x00000205D6B1F540>\n", "gc: collectable <list 0x00000205D6B1E5C0>\n", "gc: collectable <list 0x00000205D6B1F940>\n", "gc: collectable <list 0x00000205D6B2F680>\n", "gc: collectable <list 0x00000205D6B2F640>\n", "gc: collectable <list 0x00000205D6B2C080>\n", "gc: collectable <list 0x00000205D6B2C100>\n", "gc: collectable <list 0x00000205D6B2C1C0>\n", "gc: collectable <list 0x00000205D6B2C240>\n", "gc: collectable <list 0x00000205D6B2C2C0>\n", "gc: collectable <list 0x00000205D6B2C340>\n", "gc: collectable <list 0x00000205D6B2C3C0>\n", "gc: collectable <list 0x00000205D6B2C440>\n", "gc: collectable <list 0x00000205D6B2C180>\n", "gc: collectable <list 0x00000205D6B2C640>\n", "gc: collectable <list 0x00000205D6B2C4C0>\n", "gc: collectable <list 0x00000205D6B2C7C0>\n", "gc: collectable <list 0x00000205D6B2C6C0>\n", "gc: collectable <list 0x00000205D6B2C740>\n", "gc: collectable <list 0x00000205D6B2C940>\n", "gc: collectable <list 0x00000205D6B2CA80>\n", "gc: collectable <list 0x00000205D6B2CA00>\n", "gc: collectable <list 0x00000205D6B2CB40>\n", "gc: collectable <list 0x00000205D6B2CC40>\n", "gc: collectable <list 0x00000205D6B2CD00>\n", "gc: collectable <list 0x00000205D6B2CD80>\n", "gc: collectable <list 0x00000205D6B2CF40>\n", "gc: collectable <list 0x00000205D6B2CDC0>\n", "gc: collectable <list 0x00000205D6B2CE40>\n", "gc: collectable <list 0x00000205D6B2CEC0>\n", "gc: collectable <list 0x00000205D6B2D180>\n", "gc: collectable <list 0x00000205D6B2D200>\n", "gc: collectable <list 0x00000205D6B2D080>\n", "gc: collectable <list 0x00000205D6B2D2C0>\n", "gc: collectable <list 0x00000205D6B2D340>\n", "gc: collectable <list 0x00000205D6B2D4C0>\n", "gc: collectable <list 0x00000205D6B2D440>\n", "gc: collectable <list 0x00000205D6B2D3C0>\n", "gc: collectable <list 0x00000205D6B2D580>\n", "gc: collectable <list 0x00000205D6B2D6C0>\n", "gc: collectable <list 0x00000205D6B2D640>\n", "gc: collectable <list 0x00000205D6B2CE00>\n", "gc: collectable <list 0x00000205D6B2C8C0>\n", "gc: collectable <list 0x00000205D6B2D500>\n", "gc: collectable <list 0x00000205D6B2D900>\n", "gc: collectable <list 0x00000205D6B2DB40>\n", "gc: collectable <list 0x00000205D6B2D980>\n", "gc: collectable <list 0x00000205D6B2D800>\n", "gc: collectable <list 0x00000205D6B2DAC0>\n", "gc: collectable <list 0x00000205D6B2DC80>\n", "gc: collectable <list 0x00000205D6B2DD40>\n", "gc: collectable <list 0x00000205D6B2DDC0>\n", "gc: collectable <list 0x00000205D6B2DE40>\n", "gc: collectable <list 0x00000205D6B2DEC0>\n", "gc: collectable <list 0x00000205D6B2DF40>\n", "gc: collectable <list 0x00000205D6B2DA00>\n", "gc: collectable <list 0x00000205D6B2D100>\n", "gc: collectable <list 0x00000205D6B2E040>\n", "gc: collectable <list 0x00000205D6B2D780>\n", "gc: collectable <list 0x00000205D6B2E240>\n", "gc: collectable <list 0x00000205D6B2CB80>\n", "gc: collectable <list 0x00000205D6B2E300>\n", "gc: collectable <list 0x00000205D6B2D9C0>\n", "gc: collectable <tuple 0x00000205D65ED060>\n", "gc: collectable <_ctypes.PyCArrayType 0x00000205D597D480>\n", "gc: collectable <tuple 0x00000205D6B52520>\n", "gc: collectable <getset_descriptor 0x00000205D6B1D640>\n", "gc: collectable <getset_descriptor 0x00000205D6B62540>\n", "gc: collectable <StgDict 0x00000205D6B3B370>\n", "gc: collectable <tuple 0x00000205D65ED930>\n", "gc: collectable <_ctypes.PyCArrayType 0x00000205D597B1A0>\n", "gc: collectable <tuple 0x00000205D6B52160>\n", "gc: collectable <getset_descriptor 0x00000205D6B620C0>\n", "gc: collectable <getset_descriptor 0x00000205D6B62E00>\n", "gc: collectable <StgDict 0x00000205D6B7A9B0>\n", "gc: collectable <function 0x00000205D6B4DE40>\n", "gc: collectable <function 0x00000205D6B4DEE0>\n", "gc: collectable <tuple 0x00000205D65ED570>\n", "gc: collectable <dict 0x00000205D6B61D00>\n", "gc: collectable <type 0x00000205D597B580>\n", "gc: collectable <tuple 0x00000205D6B629C0>\n", "gc: collectable <getset_descriptor 0x00000205D6B59A80>\n", "gc: collectable <getset_descriptor 0x00000205D6B5ACC0>\n", "gc: collectable <MyObject 0x00000205D64A52E0>\n", "gc: collectable <MyObject 0x00000205D65E8C80>\n", "gc: collectable <MyObject 0x00000205D65E9AC0>\n", "gc: collectable <MyObject 0x00000205D65E8EC0>\n", "gc: collectable <dict 0x00000205D6B25500>\n", "gc: collectable <list 0x00000205D6ADC100>\n", "gc: collectable <list 0x00000205D6B48B80>\n", "gc: collectable <list 0x00000205D6B48BC0>\n", "gc: collectable <Line 0x00000205D647DC70>\n", "gc: collectable <Line 0x00000205D647E240>\n", "gc: collectable <Line 0x00000205D647D280>\n", "gc: collectable <list 0x00000205D6B1DA00>\n", "gc: collectable <cell 0x00000205D64A68F0>\n", "gc: collectable <RegexLexerMeta 0x00000205D5977F40>\n", "gc: collectable <tuple 0x00000205D6B1A610>\n", "gc: collectable <list 0x00000205D6B26140>\n", "gc: collectable <dict 0x00000205D6B49000>\n", "gc: collectable <tuple 0x00000205D6B45EC0>\n", "gc: collectable <tuple 0x00000205D6B45E80>\n", "gc: collectable <tuple 0x00000205D6B49080>\n", "gc: collectable <tuple 0x00000205D6B49040>\n", "gc: collectable <tuple 0x00000205D6B490C0>\n", "gc: collectable <tuple 0x00000205D6B49140>\n", "gc: collectable <tuple 0x00000205D6B49180>\n", "gc: collectable <list 0x00000205D6B48F80>\n", "gc: collectable <tuple 0x00000205D6B49BC0>\n", "gc: collectable <tuple 0x00000205D6B49540>\n", "gc: collectable <list 0x00000205D6498E00>\n", "gc: collectable <tuple 0x00000205D6B492C0>\n", "gc: collectable <tuple 0x00000205D6B491C0>\n", "gc: collectable <tuple 0x00000205D6B49240>\n", "gc: collectable <tuple 0x00000205D6B49B80>\n", "gc: collectable <builtin_method 0x00000205D6B43100>\n", "gc: collectable <tuple 0x00000205D6B49B40>\n", "gc: collectable <list 0x00000205D6442D40>\n", "gc: collectable <list 0x00000205D6B49300>\n", "gc: collectable <list 0x00000205D6B48880>\n", "gc: collectable <tuple 0x00000205D6B49440>\n", "gc: collectable <tuple 0x00000205D6B49100>\n", "gc: collectable <list 0x00000205D6B49340>\n", "gc: collectable <tuple 0x00000205D6B49280>\n", "gc: collectable <list 0x00000205D6B49600>\n", "gc: collectable <tuple 0x00000205D6B49380>\n", "gc: collectable <tuple 0x00000205D6B49900>\n", "gc: collectable <tuple 0x00000205D6B49940>\n", "gc: collectable <tuple 0x00000205D6B49A40>\n", "gc: collectable <tuple 0x00000205D6B49A80>\n", "gc: collectable <tuple 0x00000205D6B49D80>\n", "gc: collectable <list 0x00000205D6B49640>\n", "gc: collectable <list 0x00000205D6B48C00>\n", "gc: collectable <tuple 0x00000205D6B49E40>\n", "gc: collectable <list 0x00000205D6B498C0>\n", "gc: collectable <tuple 0x00000205D6B49E80>\n", "gc: collectable <tuple 0x00000205D6B4A0C0>\n", "gc: collectable <tuple 0x00000205D6B4A100>\n", "gc: collectable <tuple 0x00000205D6B4A140>\n", "gc: collectable <tuple 0x00000205D6B4A240>\n", "gc: collectable <tuple 0x00000205D6B4A340>\n", "gc: collectable <list 0x00000205D6B49840>\n", "gc: collectable <list 0x00000205D6B49680>\n", "gc: collectable <tuple 0x00000205D6B4A380>\n", "gc: collectable <tuple 0x00000205D6A1D300>\n", "gc: collectable <tuple 0x00000205D6B4A3C0>\n", "gc: collectable <list 0x00000205D6B496C0>\n", "gc: collectable <list 0x00000205D6B48D80>\n", "gc: collectable <tuple 0x00000205D6B4A500>\n", "gc: collectable <tuple 0x00000205D6B4A4C0>\n", "gc: collectable <tuple 0x00000205D6B4A580>\n", "gc: collectable <list 0x00000205D6B49800>\n", "gc: collectable <list 0x00000205D6B49880>\n", "gc: collectable <list 0x00000205D6B48B40>\n", "gc: collectable <tuple 0x00000205D6B4A680>\n", "gc: collectable <list 0x00000205D6B49980>\n", "gc: collectable <tuple 0x00000205D6B4A6C0>\n", "gc: collectable <tuple 0x00000205D6B4A780>\n", "gc: collectable <list 0x00000205D6B499C0>\n", "gc: collectable <tuple 0x00000205D6B4A9C0>\n", "gc: collectable <list 0x00000205D6B48AC0>\n", "gc: collectable <tuple 0x00000205D6B4AB00>\n", "gc: collectable <list 0x00000205D6B49AC0>\n", "gc: collectable <tuple 0x00000205D644C400>\n", "gc: collectable <tuple 0x00000205D6B4AAC0>\n", "gc: collectable <tuple 0x00000205D6B4A840>\n", "gc: collectable <tuple 0x00000205D6B4AA80>\n", "gc: collectable <tuple 0x00000205D6B4AB40>\n", "gc: collectable <list 0x00000205D6B49B00>\n", "gc: collectable <list 0x00000205D6B49400>\n", "gc: collectable <tuple 0x00000205D6B4AC40>\n", "gc: collectable <list 0x00000205D6B49EC0>\n", "gc: collectable <tuple 0x00000205D6B2BFC0>\n", "gc: collectable <tuple 0x00000205D6B4AC80>\n", "gc: collectable <tuple 0x00000205D6B4AE00>\n", "gc: collectable <tuple 0x00000205D6B4AF80>\n", "gc: collectable <tuple 0x00000205D6B4AEC0>\n", "gc: collectable <tuple 0x00000205D6B4AFC0>\n", "gc: collectable <tuple 0x00000205D6B4B000>\n", "gc: collectable <list 0x00000205D6B49F00>\n", "gc: collectable <list 0x00000205D6B48E40>\n", "gc: collectable <tuple 0x00000205D6B4B040>\n", "gc: collectable <list 0x00000205D6B4A180>\n", "gc: collectable <tuple 0x00000205D6A01480>\n", "gc: collectable <tuple 0x00000205D6B4B0C0>\n", "gc: collectable <tuple 0x00000205D6B4B100>\n", "gc: collectable <tuple 0x00000205D6B4B140>\n", "gc: collectable <tuple 0x00000205D6B4B180>\n", "gc: collectable <tuple 0x00000205D6B4B200>\n", "gc: collectable <tuple 0x00000205D6B4B240>\n", "gc: collectable <list 0x00000205D6B4A1C0>\n", "gc: collectable <list 0x00000205D6B48CC0>\n", "gc: collectable <tuple 0x00000205D6B4B280>\n", "gc: collectable <tuple 0x00000205D6B4B2C0>\n", "gc: collectable <tuple 0x00000205D6B4B340>\n", "gc: collectable <list 0x00000205D6B4A2C0>\n", "gc: collectable <list 0x00000205D6B4A300>\n", "gc: collectable <tuple 0x00000205D6B4B300>\n", "gc: collectable <tuple 0x00000205D6B2BD40>\n", "gc: collectable <tuple 0x00000205D6B4B3C0>\n", "gc: collectable <list 0x00000205D6B49C80>\n", "gc: collectable <tuple 0x00000205D6B4B380>\n", "gc: collectable <list 0x00000205D6B4A400>\n", "gc: collectable <tuple 0x00000205D6B4B440>\n", "gc: collectable <list 0x00000205D6B4A440>\n", "gc: collectable <tuple 0x00000205D6B4B4C0>\n", "gc: collectable <list 0x00000205D6B4A080>\n", "gc: collectable <tuple 0x00000205D6B4B500>\n", "gc: collectable <tuple 0x00000205D6B4B580>\n", "gc: collectable <list 0x00000205D6B4A540>\n", "gc: collectable <tuple 0x00000205D69F7A80>\n", "gc: collectable <tuple 0x00000205D6B4B5C0>\n", "gc: collectable <tuple 0x00000205D6B4B600>\n", "gc: collectable <tuple 0x00000205D6B4B640>\n", "gc: collectable <tuple 0x00000205D6B4B680>\n", "gc: collectable <tuple 0x00000205D6B4B700>\n", "gc: collectable <tuple 0x00000205D6B4B740>\n", "gc: collectable <tuple 0x00000205D6B4B780>\n", "gc: collectable <builtin_method 0x00000205D6B50680>\n", "gc: collectable <tuple 0x00000205D6B4B7C0>\n", "gc: collectable <list 0x00000205D6B4A5C0>\n", "gc: collectable <tuple 0x00000205D6B540C0>\n", "gc: collectable <tuple 0x00000205D6B4B940>\n", "gc: collectable <list 0x00000205D6B4A040>\n", "gc: collectable <Executing 0x00000205D64A7D70>\n", "gc: collectable <dict 0x00000205D6489E00>\n", "gc: collectable <tuple 0x00000205D65E9270>\n", "gc: collectable <builtin_method 0x00000205D6B42A70>\n", "gc: collectable <builtin_method 0x00000205D6B42AC0>\n", "gc: collectable <builtin_method 0x00000205D6B42B60>\n", "gc: collectable <builtin_method 0x00000205D6B42BB0>\n", "gc: collectable <builtin_method 0x00000205D6B42C00>\n", "gc: collectable <builtin_method 0x00000205D6B42C50>\n", "gc: collectable <builtin_method 0x00000205D6B42CA0>\n", "gc: collectable <builtin_method 0x00000205D6B42DE0>\n", "gc: collectable <builtin_method 0x00000205D6B42FC0>\n", "gc: collectable <builtin_method 0x00000205D6B42F70>\n", "gc: collectable <builtin_method 0x00000205D6B43010>\n", "gc: collectable <builtin_method 0x00000205D6B43060>\n", "gc: collectable <builtin_method 0x00000205D6B430B0>\n", "gc: collectable <builtin_method 0x00000205D6B431A0>\n", "gc: collectable <builtin_method 0x00000205D6B431F0>\n", "gc: collectable <builtin_method 0x00000205D6B43240>\n", "gc: collectable <builtin_method 0x00000205D6B43290>\n", "gc: collectable <builtin_method 0x00000205D6B432E0>\n", "gc: collectable <builtin_method 0x00000205D6B43330>\n", "gc: collectable <builtin_method 0x00000205D6B43380>\n", "gc: collectable <builtin_method 0x00000205D6B433D0>\n", "gc: collectable <builtin_method 0x00000205D6B43150>\n", "gc: collectable <builtin_method 0x00000205D6B43470>\n", "gc: collectable <builtin_method 0x00000205D6B434C0>\n", "gc: collectable <builtin_method 0x00000205D6B43510>\n", "gc: collectable <builtin_method 0x00000205D6B43560>\n", "gc: collectable <builtin_method 0x00000205D6B435B0>\n", "gc: collectable <builtin_method 0x00000205D6B43600>\n", "gc: collectable <builtin_method 0x00000205D6B43420>\n", "gc: collectable <builtin_method 0x00000205D6B436A0>\n", "gc: collectable <builtin_method 0x00000205D6B436F0>\n", "gc: collectable <builtin_method 0x00000205D6B43650>\n", "gc: collectable <builtin_method 0x00000205D6B43790>\n", "gc: collectable <builtin_method 0x00000205D6B437E0>\n", "gc: collectable <builtin_method 0x00000205D6B43740>\n", "gc: collectable <builtin_method 0x00000205D6B43880>\n", "gc: collectable <builtin_method 0x00000205D6B438D0>\n", "gc: collectable <builtin_method 0x00000205D6B43830>\n", "gc: collectable <builtin_method 0x00000205D6B43920>\n", "gc: collectable <builtin_method 0x00000205D6B43970>\n", "gc: collectable <builtin_method 0x00000205D6B439C0>\n", "gc: collectable <builtin_method 0x00000205D6B43A10>\n", "gc: collectable <builtin_method 0x00000205D6B43A60>\n", "gc: collectable <builtin_method 0x00000205D6B43AB0>\n", "gc: collectable <builtin_method 0x00000205D6B43B00>\n", "gc: collectable <builtin_method 0x00000205D6B43BA0>\n", "gc: collectable <builtin_method 0x00000205D6B43BF0>\n", "gc: collectable <builtin_method 0x00000205D6B43C40>\n", "gc: collectable <builtin_method 0x00000205D6B43C90>\n", "gc: collectable <builtin_method 0x00000205D6B43CE0>\n", "gc: collectable <builtin_method 0x00000205D6B43D30>\n", "gc: collectable <builtin_method 0x00000205D6B43D80>\n", "gc: collectable <builtin_method 0x00000205D6B43B50>\n", "gc: collectable <builtin_method 0x00000205D6B43E20>\n", "gc: collectable <builtin_method 0x00000205D6B43E70>\n", "gc: collectable <builtin_method 0x00000205D6B43EC0>\n", "gc: collectable <builtin_method 0x00000205D6B43F10>\n", "gc: collectable <builtin_method 0x00000205D6B43F60>\n", "gc: collectable <builtin_method 0x00000205D6B43FB0>\n", "gc: collectable <builtin_method 0x00000205D6B50040>\n", "gc: collectable <builtin_method 0x00000205D6B43DD0>\n", "gc: collectable <builtin_method 0x00000205D6B500E0>\n", "gc: collectable <builtin_method 0x00000205D6B50130>\n", "gc: collectable <builtin_method 0x00000205D6B50090>\n", "gc: collectable <builtin_method 0x00000205D6B501D0>\n", "gc: collectable <builtin_method 0x00000205D6B50220>\n", "gc: collectable <builtin_method 0x00000205D6B50180>\n", "gc: collectable <builtin_method 0x00000205D6B50270>\n", "gc: collectable <builtin_method 0x00000205D6B502C0>\n", "gc: collectable <builtin_method 0x00000205D6B50310>\n", "gc: collectable <builtin_method 0x00000205D6B50360>\n", "gc: collectable <builtin_method 0x00000205D6B503B0>\n", "gc: collectable <builtin_method 0x00000205D6B50400>\n", "gc: collectable <builtin_method 0x00000205D6B50450>\n", "gc: collectable <builtin_method 0x00000205D6B504A0>\n", "gc: collectable <builtin_method 0x00000205D6B504F0>\n", "gc: collectable <builtin_method 0x00000205D6B50540>\n", "gc: collectable <builtin_method 0x00000205D6B50590>\n", "gc: collectable <builtin_method 0x00000205D6B505E0>\n", "gc: collectable <builtin_method 0x00000205D6B50630>\n", "gc: collectable <builtin_method 0x00000205D6B50860>\n", "gc: collectable <builtin_method 0x00000205D6B50810>\n", "gc: collectable <function 0x00000205D6A63CE0>\n", "gc: collectable <dict 0x00000205D6498A40>\n", "gc: collectable <tuple 0x00000205D6B248C0>\n", "gc: collectable <cell 0x00000205D65EC550>\n", "gc: collectable <tuple 0x00000205D6B58800>\n", "gc: collectable <tuple 0x00000205D6B587C0>\n", "gc: collectable <tuple 0x00000205D6B672C0>\n", "gc: collectable <list 0x00000205D6B67D80>\n", "gc: collectable <tuple 0x00000205D6B628C0>\n", "gc: collectable <list 0x00000205D6B62100>\n", "gc: collectable <tuple 0x00000205D6B5B0C0>\n", "gc: collectable <list 0x00000205D6B5AF80>\n", "gc: collectable <tuple 0x00000205D6B5B000>\n", "gc: collectable <tuple 0x00000205D6B58BC0>\n", "gc: collectable <tuple 0x00000205D6B583C0>\n", "gc: collectable <list 0x00000205D6B5A540>\n", "gc: collectable <tuple 0x00000205D6B58A40>\n", "gc: collectable <tuple 0x00000205D6B59C80>\n", "gc: collectable <tuple 0x00000205D6B59640>\n", "gc: collectable <tuple 0x00000205D6B59580>\n", "gc: collectable <list 0x00000205D6B59280>\n", "gc: collectable <tuple 0x00000205D6B62500>\n", "gc: collectable <tuple 0x00000205D6B62480>\n", "gc: collectable <tuple 0x00000205D6B62BC0>\n", "gc: collectable <list 0x00000205D6B5A1C0>\n", "gc: collectable <tuple 0x00000205D6B62B80>\n", "gc: collectable <tuple 0x00000205D6B62B40>\n", "gc: collectable <list 0x00000205D6B58B80>\n", "gc: collectable <tuple 0x00000205D6B61780>\n", "gc: collectable <tuple 0x00000205D6B619C0>\n", "gc: collectable <list 0x00000205D6B5A340>\n", "gc: collectable <tuple 0x00000205D6B61540>\n", "gc: collectable <list 0x00000205D6B59E00>\n", "gc: collectable <tuple 0x00000205D6B60FC0>\n", "gc: collectable <tuple 0x00000205D6B60F00>\n", "gc: collectable <tuple 0x00000205D6B60B00>\n", "gc: collectable <tuple 0x00000205D6B60B40>\n", "gc: collectable <tuple 0x00000205D6B61A80>\n", "gc: collectable <list 0x00000205D6B5B280>\n", "gc: collectable <tuple 0x00000205D6B61880>\n", "gc: collectable <tuple 0x00000205D6B618C0>\n", "gc: collectable <tuple 0x00000205D6B61680>\n", "gc: collectable <tuple 0x00000205D6B61100>\n", "gc: collectable <tuple 0x00000205D6B610C0>\n", "gc: collectable <tuple 0x00000205D6B5A600>\n", "gc: collectable <type 0x00000205D597C8E0>\n", "gc: collectable <tuple 0x00000205D6B5AEC0>\n", "gc: collectable <MyObject 0x00000205D65C6C60>\n", "gc: collectable <MyObject 0x00000205D647D520>\n", "gc: collectable <builtin_method 0x00000205D6B50B80>\n", "gc: collectable <builtin_method 0x00000205D6B50E00>\n", "gc: collectable <builtin_method 0x00000205D6B50F90>\n", "gc: collectable <builtin_method 0x00000205D6B51940>\n", "gc: collectable <builtin_method 0x00000205D6B52840>\n", "gc: collectable <builtin_method 0x00000205D6B527F0>\n", "gc: collectable <builtin_method 0x00000205D6B52890>\n", "gc: collectable <builtin_method 0x00000205D6B528E0>\n", "gc: collectable <builtin_method 0x00000205D6B52930>\n", "gc: collectable <builtin_method 0x00000205D6B52980>\n", "gc: collectable <builtin_method 0x00000205D6B529D0>\n", "gc: collectable <builtin_method 0x00000205D6B52A20>\n", "gc: collectable <builtin_method 0x00000205D6B52A70>\n", "gc: collectable <builtin_method 0x00000205D6B52AC0>\n", "gc: collectable <builtin_method 0x00000205D6B52B10>\n", "gc: collectable <builtin_method 0x00000205D6B52B60>\n", "gc: collectable <builtin_method 0x00000205D6B52BB0>\n", "gc: collectable <builtin_method 0x00000205D6B52C00>\n", "gc: collectable <builtin_method 0x00000205D6B52C50>\n", "gc: collectable <builtin_method 0x00000205D6B52CA0>\n", "gc: collectable <builtin_method 0x00000205D6B52CF0>\n", "gc: collectable <builtin_method 0x00000205D6B52D40>\n", "gc: collectable <builtin_method 0x00000205D6B52D90>\n", "gc: collectable <builtin_method 0x00000205D6B52DE0>\n", "gc: collectable <builtin_method 0x00000205D6B52E30>\n", "gc: collectable <builtin_method 0x00000205D6B52E80>\n", "gc: collectable <builtin_method 0x00000205D6B52ED0>\n", "gc: collectable <builtin_method 0x00000205D6B52F20>\n", "gc: collectable <builtin_method 0x00000205D6B52F70>\n", "gc: collectable <builtin_method 0x00000205D6B52FC0>\n", "gc: collectable <list 0x00000205D64A3D40>\n", "gc: collectable <dict 0x00000205D6498FC0>\n", "gc: collectable <function 0x00000205D6A63C40>\n", "gc: collectable <function 0x00000205D6A637E0>\n", "gc: collectable <getset_descriptor 0x00000205D6B60900>\n", "gc: collectable <getset_descriptor 0x00000205D6B62F40>\n"]}, {"data": {"text/plain": ["592"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["## Handled Circular reference\n", "import gc\n", "\n", "class MyObject:\n", "    def __init__(self, name):\n", "        self.name = name\n", "        print(f\"Object {self.name} created\")\n", "\n", "    def __del__(self):\n", "        print(f\"Object {self.name} deleted\")\n", "\n", "# Create circular reference\n", "obj1 = MyObject(\"obj1\")\n", "obj2 = MyObject(\"obj2\")\n", "obj1.ref = obj2\n", "obj2.ref = obj1\n", "\n", "del obj1\n", "del obj2\n", "\n", "## Manually trigger the garbage collection\n", "gc.collect()\n", "\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n", "1\n", "2\n", "3\n", "4\n", "5\n", "6\n", "7\n", "8\n", "9\n", "10\n", "11\n"]}], "source": ["## Generators For Memory Efficiency\n", "#Generators allow you to produce items one at a time, using memory efficiently by only keeping one item in memory at a time.\n", "\n", "def generate_numbers(n):\n", "    for i in range(n):\n", "        yield i\n", "\n", "## using the generator\n", "for num in generate_numbers(100000):\n", "    print(num)\n", "    if num>10:\n", "        break"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["## Profiling Memory USage with tracemalloc\n", "import tracemalloc\n", "\n", "def create_list():\n", "    return [i for i in range(10000)]\n", "\n", "def main():\n", "    tracemalloc.start()\n", "    \n", "    create_list()\n", "    \n", "    snapshot = tracemalloc.take_snapshot()\n", "    top_stats = snapshot.statistics('lineno')\n", "    \n", "    print(\"[ Top 10 ]\")\n", "    for stat in top_stats[::]:\n", "        print(stat)\n"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ Top 10 ]\n", "e:\\UDemy Final\\python\\venv\\Lib\\selectors.py:314: size=144 KiB, count=3, average=48.0 KiB\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\compilerop.py:174: size=11.2 KiB, count=122, average=94 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\builtin_trap.py:70: size=6512 B, count=1, average=6512 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\json\\decoder.py:353: size=5812 B, count=92, average=63 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\threading.py:272: size=3800 B, count=10, average=380 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\codeop.py:118: size=3509 B, count=47, average=75 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\zmq\\sugar\\attrsettr.py:45: size=3431 B, count=73, average=47 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\compilerop.py:86: size=3395 B, count=52, average=65 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\traitlets\\traitlets.py:731: size=2883 B, count=46, average=63 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\jupyter_client\\jsonutil.py:111: size=2050 B, count=41, average=50 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\traitlets\\traitlets.py:1543: size=2031 B, count=33, average=62 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\jupyter_client\\jsonutil.py:75: size=1456 B, count=7, average=208 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\re\\_compiler.py:759: size=1360 B, count=5, average=272 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\iostream.py:346: size=1328 B, count=16, average=83 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\traitlets\\traitlets.py:1514: size=1320 B, count=11, average=120 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\jupyter_client\\session.py:1057: size=1276 B, count=11, average=116 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\jupyter_client\\session.py:100: size=1243 B, count=8, average=155 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\zmq\\sugar\\socket.py:369: size=1152 B, count=5, average=230 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\zmq\\sugar\\socket.py:809: size=1056 B, count=6, average=176 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\threading.py:568: size=864 B, count=16, average=54 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\threading.py:917: size=800 B, count=20, average=40 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\iostream.py:276: size=776 B, count=8, average=97 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\compiler.py:91: size=726 B, count=7, average=104 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\asyncio\\events.py:84: size=648 B, count=6, average=108 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\_weakrefset.py:88: size=640 B, count=8, average=80 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\threading.py:1349: size=640 B, count=4, average=160 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\iostream.py:287: size=632 B, count=7, average=90 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\iostream.py:154: size=568 B, count=1, average=568 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\interactiveshell.py:3104: size=520 B, count=4, average=130 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\threading.py:1027: size=480 B, count=8, average=60 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\history.py:782: size=440 B, count=2, average=220 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\kernelbase.py:775: size=424 B, count=2, average=212 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\history.py:836: size=416 B, count=6, average=69 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\zmq\\sugar\\context.py:354: size=416 B, count=4, average=104 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\history.py:790: size=416 B, count=1, average=416 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\history.py:789: size=416 B, count=1, average=416 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\fnmatch.py:46: size=400 B, count=5, average=80 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\history.py:783: size=393 B, count=1, average=393 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\interactiveshell.py:3334: size=376 B, count=1, average=376 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\threading.py:262: size=360 B, count=5, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\threading.py:261: size=360 B, count=5, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\ipkernel.py:362: size=360 B, count=1, average=360 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\threading.py:269: size=344 B, count=3, average=115 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\dateutil\\parser\\_parser.py:1195: size=336 B, count=7, average=48 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\threading.py:912: size=320 B, count=8, average=40 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\interactiveshell.py:3566: size=320 B, count=5, average=64 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\asyncio\\base_events.py:792: size=312 B, count=3, average=104 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\iostream.py:527: size=304 B, count=4, average=76 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\interactiveshell.py:3517: size=296 B, count=1, average=296 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\tornado\\queues.py:248: size=288 B, count=2, average=144 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\asyncio\\base_events.py:427: size=288 B, count=2, average=144 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\iostream.py:142: size=288 B, count=1, average=288 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\fnmatch.py:185: size=280 B, count=5, average=56 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\fnmatch.py:52: size=280 B, count=5, average=56 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\kernelbase.py:534: size=264 B, count=1, average=264 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\kernelbase.py:435: size=248 B, count=1, average=248 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\iostream.py:121: size=248 B, count=1, average=248 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\threading.py:894: size=244 B, count=4, average=61 B\n", "<frozen ntpath>:66: size=240 B, count=5, average=48 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\zmq\\sugar\\socket.py:806: size=240 B, count=3, average=80 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\logging\\__init__.py:1622: size=240 B, count=1, average=240 B\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_37980\\3835544042.py:7: size=232 B, count=2, average=116 B\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_37980\\3835544042.py:4: size=232 B, count=2, average=116 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\kernelbase.py:545: size=232 B, count=1, average=232 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\zmqshell.py:549: size=224 B, count=3, average=75 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\compilerop.py:171: size=216 B, count=3, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\interactiveshell.py:3223: size=208 B, count=4, average=52 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\tornado\\platform\\asyncio.py:235: size=208 B, count=3, average=69 B\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_37980\\811388706.py:22: size=208 B, count=2, average=104 B\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_37980\\3581627739.py:22: size=208 B, count=2, average=104 B\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_37980\\2565588716.py:22: size=208 B, count=2, average=104 B\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_37980\\2547200138.py:22: size=208 B, count=2, average=104 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\jupyter_client\\session.py:1085: size=208 B, count=1, average=208 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\kernelbase.py:770: size=208 B, count=1, average=208 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\asyncio\\futures.py:418: size=200 B, count=5, average=40 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\jupyter_client\\session.py:688: size=194 B, count=2, average=97 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\jupyter_client\\session.py:750: size=192 B, count=2, average=96 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\interactiveshell.py:3224: size=176 B, count=4, average=44 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\kernelbase.py:1182: size=173 B, count=2, average=86 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\compilerop.py:172: size=168 B, count=6, average=28 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\threading.py:969: size=160 B, count=1, average=160 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\kernelbase.py:570: size=160 B, count=1, average=160 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\interactiveshell.py:3577: size=160 B, count=1, average=160 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\interactiveshell.py:3493: size=160 B, count=1, average=160 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\interactiveshell.py:3237: size=160 B, count=1, average=160 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\asyncio\\futures.py:394: size=160 B, count=1, average=160 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\asyncio\\futures.py:387: size=160 B, count=1, average=160 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\asyncio\\futures.py:381: size=160 B, count=1, average=160 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\iostream.py:722: size=144 B, count=2, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\async_helpers.py:151: size=142 B, count=3, average=47 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\threading.py:1016: size=140 B, count=5, average=28 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\interactiveshell.py:3509: size=128 B, count=3, average=43 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\jupyter_client\\jsonutil.py:73: size=128 B, count=2, average=64 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\threading.py:121: size=120 B, count=2, average=60 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\traitlets\\traitlets.py:1540: size=120 B, count=2, average=60 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\kernelbase.py:318: size=120 B, count=2, average=60 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\zmq\\eventloop\\zmqstream.py:652: size=120 B, count=1, average=120 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\inspect.py:265: size=120 B, count=1, average=120 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\asyncio\\base_events.py:755: size=120 B, count=1, average=120 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\threading.py:1020: size=112 B, count=4, average=28 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\ipkernel.py:770: size=112 B, count=4, average=28 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\jupyter_client\\session.py:989: size=111 B, count=2, average=56 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\zmq\\sugar\\socket.py:755: size=96 B, count=2, average=48 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\interactiveshell.py:3375: size=96 B, count=2, average=48 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\warnings.py:189: size=96 B, count=1, average=96 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\history.py:805: size=90 B, count=2, average=45 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\jupyter_client\\session.py:996: size=88 B, count=2, average=44 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\tracemalloc.py:551: size=72 B, count=1, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\tracemalloc.py:528: size=72 B, count=1, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\tracemalloc.py:477: size=72 B, count=1, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\tracemalloc.py:420: size=72 B, count=1, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\tracemalloc.py:312: size=72 B, count=1, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\tracemalloc.py:225: size=72 B, count=1, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\tracemalloc.py:220: size=72 B, count=1, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\tracemalloc.py:215: size=72 B, count=1, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\tracemalloc.py:212: size=72 B, count=1, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\tracemalloc.py:203: size=72 B, count=1, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\tracemalloc.py:187: size=72 B, count=1, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\tracemalloc.py:172: size=72 B, count=1, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\tracemalloc.py:155: size=72 B, count=1, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\tracemalloc.py:151: size=72 B, count=1, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\tracemalloc.py:147: size=72 B, count=1, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\tracemalloc.py:66: size=72 B, count=1, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\tracemalloc.py:52: size=72 B, count=1, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\tracemalloc.py:37: size=72 B, count=1, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\tracemalloc.py:13: size=72 B, count=1, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\threading.py:271: size=72 B, count=1, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\threading.py:267: size=72 B, count=1, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\history.py:851: size=72 B, count=1, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\history.py:837: size=72 B, count=1, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\history.py:834: size=72 B, count=1, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\json\\encoder.py:224: size=72 B, count=1, average=72 B\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_37980\\451043146.py:0: size=72 B, count=1, average=72 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\asyncio\\base_events.py:1935: size=64 B, count=2, average=32 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\zmq\\sugar\\socket.py:810: size=64 B, count=1, average=64 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\zmq\\eventloop\\zmqstream.py:640: size=64 B, count=1, average=64 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\zmq\\eventloop\\zmqstream.py:561: size=64 B, count=1, average=64 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\jupyter_client\\session.py:1053: size=64 B, count=1, average=64 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\kernelbase.py:721: size=64 B, count=1, average=64 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\kernelbase.py:69: size=64 B, count=1, average=64 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\ipkernel.py:384: size=64 B, count=1, average=64 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\ipkernel.py:383: size=64 B, count=1, average=64 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\ipkernel.py:381: size=64 B, count=1, average=64 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\ipkernel.py:294: size=64 B, count=1, average=64 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\ipkernel.py:291: size=64 B, count=1, average=64 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\decorator.py:232: size=64 B, count=1, average=64 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\interactiveshell.py:3303: size=64 B, count=1, average=64 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\interactiveshell.py:3300: size=64 B, count=1, average=64 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\tokenize.py:537: size=56 B, count=1, average=56 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\traitlets\\traitlets.py:1534: size=56 B, count=1, average=56 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\jupyter_client\\session.py:1088: size=56 B, count=1, average=56 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\jupyter_client\\session.py:738: size=56 B, count=1, average=56 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\interactiveshell.py:3498: size=56 B, count=1, average=56 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\history.py:866: size=56 B, count=1, average=56 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\completer.py:1120: size=56 B, count=1, average=56 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\selectors.py:323: size=56 B, count=1, average=56 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\jupyter_client\\session.py:200: size=48 B, count=1, average=48 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\interactiveshell.py:3505: size=48 B, count=1, average=48 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\history.py:835: size=48 B, count=1, average=48 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\concurrent\\futures\\_base.py:330: size=48 B, count=1, average=48 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\zmq\\sugar\\attrsettr.py:29: size=47 B, count=1, average=47 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\prefilter.py:317: size=47 B, count=1, average=47 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\jupyter_client\\session.py:603: size=32 B, count=1, average=32 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\iostream.py:637: size=32 B, count=1, average=32 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\ipykernel\\iostream.py:168: size=32 B, count=1, average=32 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\interactiveshell.py:3503: size=32 B, count=1, average=32 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\history.py:793: size=32 B, count=1, average=32 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\completer.py:2813: size=32 B, count=1, average=32 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\completer.py:881: size=32 B, count=1, average=32 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\compilerop.py:192: size=32 B, count=1, average=32 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\concurrent\\futures\\_base.py:421: size=32 B, count=1, average=32 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\asyncio\\base_events.py:758: size=32 B, count=1, average=32 B\n", "e:\\UDemy Final\\python\\venv\\Lib\\site-packages\\IPython\\core\\interactiveshell.py:3485: size=8 B, count=1, average=8 B\n"]}], "source": ["main()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}