{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### Numpy\n", "NumPy is a fundamental library for scientific computing in Python. It provides support for arrays and matrices, along with a collection of mathematical functions to operate on these data structures. In this lesson, we will cover the basics of NumPy, focusing on arrays and vectorized operations."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: numpy in e:\\udemy final\\python\\venv\\lib\\site-packages (1.26.4)\n"]}], "source": ["!pip install numpy"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1 2 3 4 5]\n", "<class 'numpy.ndarray'>\n", "(5,)\n"]}], "source": ["import numpy as np\n", "\n", "## create array using numpy\n", "##create a 1D array\n", "arr1=np.array([1,2,3,4,5])\n", "print(arr1)\n", "print(type(arr1))\n", "print(arr1.shape)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1, 2, 3, 4, 5]])"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["## 1 d array\n", "arr2=np.array([1,2,3,4,5])\n", "arr2.reshape(1,5)  ##1 row and 5 columns"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1, 5)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["arr2=np.array([[1,2,3,4,5]])\n", "arr2.shape"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[1 2 3 4 5]\n", " [2 3 4 5 6]]\n", "(2, 5)\n"]}], "source": ["## 2d array\n", "arr2=np.array([[1,2,3,4,5],[2,3,4,5,6]])\n", "print(arr2)\n", "print(arr2.shape)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0],\n", "       [2],\n", "       [4],\n", "       [6],\n", "       [8]])"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["np.arange(0,10,2).reshape(5,1)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1., 1., 1., 1.],\n", "       [1., 1., 1., 1.],\n", "       [1., 1., 1., 1.]])"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["np.ones((3,4))"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1., 0., 0.],\n", "       [0., 1., 0.],\n", "       [0., 0., 1.]])"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["## identity matrix\n", "np.eye(3)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Array:\n", " [[1 2 3]\n", " [4 5 6]]\n", "Shape: (2, 3)\n", "Number of dimensions: 2\n", "Size (number of elements): 6\n", "Data type: int32\n", "Item size (in bytes): 4\n"]}], "source": ["## Attributes of Numpy Array\n", "arr = np.array([[1, 2, 3], [4, 5, 6]])\n", "\n", "print(\"Array:\\n\", arr)\n", "print(\"Shape:\", arr.shape)  # Output: (2, 3)\n", "print(\"Number of dimensions:\", arr.ndim)  # Output: 2\n", "print(\"Size (number of elements):\", arr.size)  # Output: 6\n", "print(\"Data type:\", arr.dtype)  # Output: int32 (may vary based on platform)\n", "print(\"Item size (in bytes):\", arr.itemsize)  # Output: 8 (may vary based on platform)\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Addition: [11 22 33 44 55]\n", "Substraction: [ -9 -18 -27 -36 -45]\n", "Multiplication: [ 10  40  90 160 250]\n", "Division: [0.1 0.1 0.1 0.1 0.1]\n"]}], "source": ["### Numpy Vectorized Operation\n", "arr1=np.array([1,2,3,4,5])\n", "arr2=np.array([10,20,30,40,50])\n", "\n", "### Element Wise addition\n", "print(\"Addition:\", arr1+arr2)\n", "\n", "## Element Wise Substraction\n", "print(\"Substraction:\", arr1-arr2)\n", "\n", "# Element-wise multiplication\n", "print(\"Multiplication:\", arr1 * arr2)\n", "\n", "# Element-wise division\n", "print(\"Division:\", arr1 / arr2)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1.41421356 1.73205081 2.         2.23606798 2.44948974]\n", "[  7.3890561   20.08553692  54.59815003 148.4131591  403.42879349]\n", "[ 0.90929743  0.14112001 -0.7568025  -0.95892427 -0.2794155 ]\n", "[0.69314718 1.09861229 1.38629436 1.60943791 1.79175947]\n"]}], "source": ["## Universal Function\n", "arr=np.array([2,3,4,5,6])\n", "## square root\n", "print(np.sqrt(arr))\n", "\n", "## Exponential\n", "print(np.exp(arr))\n", "\n", "## <PERSON>e\n", "print(np.sin(arr))\n", "\n", "## natural log\n", "print(np.log(arr))"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Array : \n", " [[ 1  2  3  4]\n", " [ 5  6  7  8]\n", " [ 9 10 11 12]]\n"]}], "source": ["## array slicing and Indexing\n", "\n", "arr=np.array([[1,2,3,4],[5,6,7,8],[9,10,11,12]])\n", "print(\"Array : \\n\", arr)\n", "\n"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[ 6  7]\n", " [10 11]]\n"]}], "source": ["print(arr[1:,1:3])"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "[[3 4]\n", " [7 8]]\n"]}], "source": ["print(arr[0][0])\n", "print(arr[0:2,2:])"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 7,  8],\n", "       [11, 12]])"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["arr[1:,2:]"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[100   2   3   4]\n", " [  5   6   7   8]\n", " [  9  10  11  12]]\n"]}], "source": ["## Modify array elements\n", "arr[0,0]=100\n", "print(arr)"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[100   2   3   4]\n", " [100 100 100 100]\n", " [100 100 100 100]]\n"]}], "source": ["arr[1:]=100\n", "print(arr)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Normalized data: [-1.41421356 -0.70710678  0.          0.70710678  1.41421356]\n"]}], "source": ["### statistical concepts--Normalization\n", "##to have a mean of 0 and standard deviation of 1\n", "data = np.array([1, 2, 3, 4, 5])\n", "\n", "# Calculate the mean and standard deviation\n", "mean = np.mean(data)\n", "std_dev = np.std(data)\n", "\n", "# Normalize the data\n", "normalized_data = (data - mean) / std_dev\n", "print(\"Normalized data:\", normalized_data)\n", "\n"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mean: 5.5\n", "Median: 5.5\n", "Standard Deviation: 2.8722813232690143\n", "Variance: 8.25\n"]}], "source": ["data = np.array([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])\n", "\n", "# Mean\n", "mean = np.mean(data)\n", "print(\"Mean:\", mean)\n", "\n", "# Median\n", "median = np.median(data)\n", "print(\"Median:\", median)\n", "\n", "# Standard deviation\n", "std_dev = np.std(data)\n", "print(\"Standard Deviation:\", std_dev)\n", "\n", "# Variance\n", "variance = np.var(data)\n", "print(\"Variance:\", variance)\n", "\n"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([5, 6, 7, 8])"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["## Logical operation\n", "data=np.array([1,2,3,4,5,6,7,8,9,10])\n", "\n", "data[(data>=5) & (data<=8)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}