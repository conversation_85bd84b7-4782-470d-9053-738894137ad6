{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### Pandas-DataFrame And Series\n", "Pandas is a powerful data manipulation library in Python, widely used for data analysis and data cleaning. It provides two primary data structures: Series and DataFrame. A Series is a one-dimensional array-like object, while a DataFrame is a two-dimensional, size-mutable, and potentially heterogeneous tabular data structure with labeled axes (rows and columns)."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Series \n", " 0    1\n", "1    2\n", "2    3\n", "3    4\n", "4    5\n", "dtype: int64\n", "<class 'pandas.core.series.Series'>\n"]}], "source": ["## Series\n", "##A Pandas Series is a one-dimensional array-like object that can hold any data type. It is similar to a column in a table.\n", "\n", "import pandas as pd\n", "data=[1,2,3,4,5]\n", "series=pd.Series(data)\n", "print(\"Series \\n\",series)\n", "print(type(series))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["a    1\n", "b    2\n", "c    3\n", "dtype: int64\n"]}], "source": ["## Create a Series from dictionary\n", "data={'a':1,'b':2,'c':3}\n", "series_dict=pd.Series(data)\n", "print(series_dict)\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["a    10\n", "b    20\n", "c    30\n", "dtype: int64"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["data=[10,20,30]\n", "index=['a','b','c']\n", "pd.Series(data,index=index)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    Name  Age       City\n", "0  Krish   25  Bangalore\n", "1   John   30   New York\n", "2   Jack   45    Florida\n", "<class 'pandas.core.frame.DataFrame'>\n"]}], "source": ["## Dataframe\n", "## create a Dataframe from a dictionary oof list\n", "\n", "data={\n", "    'Name':['<PERSON><PERSON>','<PERSON>','<PERSON>'],\n", "    'Age':[25,30,45],\n", "    'City':['Bangalore','New York','Florida']\n", "}\n", "df=pd.DataFrame(data)\n", "print(df)\n", "print(type(df))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    Name  Age       City\n", "0  Krish   32  Bangalore\n", "1   John   34  Bangalore\n", "2  Bappy   32  Bangalore\n", "3   JAck   32  Bangalore\n", "<class 'pandas.core.frame.DataFrame'>\n"]}], "source": ["## Create a Data frame From a List of Dictionaries\n", "\n", "data=[\n", "    {'Name':'<PERSON><PERSON>','Age':32,'City':'Bangalore'},\n", "    {'Name':'<PERSON>','Age':34,'City':'Bangalore'},\n", "    {'Name':'Bappy','Age':32,'City':'Bangalore'},\n", "    {'Name':'<PERSON><PERSON><PERSON>','Age':32,'City':'Bangalore'}\n", "    \n", "]\n", "df=pd.DataFrame(data)\n", "print(df)\n", "print(type(df))"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Transaction ID</th>\n", "      <th>Date</th>\n", "      <th>Product Category</th>\n", "      <th>Product Name</th>\n", "      <th>Units Sold</th>\n", "      <th>Unit Price</th>\n", "      <th>Total Revenue</th>\n", "      <th>Region</th>\n", "      <th>Payment Method</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10001</td>\n", "      <td>2024-01-01</td>\n", "      <td>Electronics</td>\n", "      <td>iPhone 14 Pro</td>\n", "      <td>2</td>\n", "      <td>999.99</td>\n", "      <td>1999.98</td>\n", "      <td>North America</td>\n", "      <td>Credit Card</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10002</td>\n", "      <td>2024-01-02</td>\n", "      <td>Home Appliances</td>\n", "      <td>Dyson V11 Vacuum</td>\n", "      <td>1</td>\n", "      <td>499.99</td>\n", "      <td>499.99</td>\n", "      <td>Europe</td>\n", "      <td>PayPal</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>10003</td>\n", "      <td>2024-01-03</td>\n", "      <td>Clothing</td>\n", "      <td>Levi's 501 Jeans</td>\n", "      <td>3</td>\n", "      <td>69.99</td>\n", "      <td>209.97</td>\n", "      <td>Asia</td>\n", "      <td>Debit Card</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>10004</td>\n", "      <td>2024-01-04</td>\n", "      <td>Books</td>\n", "      <td>The Da Vinci Code</td>\n", "      <td>4</td>\n", "      <td>15.99</td>\n", "      <td>63.96</td>\n", "      <td>North America</td>\n", "      <td>Credit Card</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>10005</td>\n", "      <td>2024-01-05</td>\n", "      <td>Beauty Products</td>\n", "      <td>Neutrogena Skincare Set</td>\n", "      <td>1</td>\n", "      <td>89.99</td>\n", "      <td>89.99</td>\n", "      <td>Europe</td>\n", "      <td>PayPal</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Transaction ID        Date Product Category             Product Name  \\\n", "0           10001  2024-01-01      Electronics            iPhone 14 Pro   \n", "1           10002  2024-01-02  Home Appliances         Dyson V11 Vacuum   \n", "2           10003  2024-01-03         <PERSON><PERSON><PERSON>'s 501 Jeans   \n", "3           10004  2024-01-04            Books        The Da Vinci Code   \n", "4           10005  2024-01-05  Beauty Products  Neutrogena Skincare Set   \n", "\n", "   Units Sold  Unit Price  Total Revenue         Region Payment Method  \n", "0           2      999.99        1999.98  North America    Credit Card  \n", "1           1      499.99         499.99         Europe         PayPal  \n", "2           3       69.99         209.97           Asia     Debit Card  \n", "3           4       15.99          63.96  North America    Credit Card  \n", "4           1       89.99          89.99         Europe         PayPal  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["df=pd.read_csv('sales_data.csv')\n", "df.head(5)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Transaction ID</th>\n", "      <th>Date</th>\n", "      <th>Product Category</th>\n", "      <th>Product Name</th>\n", "      <th>Units Sold</th>\n", "      <th>Unit Price</th>\n", "      <th>Total Revenue</th>\n", "      <th>Region</th>\n", "      <th>Payment Method</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>235</th>\n", "      <td>10236</td>\n", "      <td>2024-08-23</td>\n", "      <td>Home Appliances</td>\n", "      <td>Nespresso Vertuo Next Coffee and Espresso Maker</td>\n", "      <td>1</td>\n", "      <td>159.99</td>\n", "      <td>159.99</td>\n", "      <td>Europe</td>\n", "      <td>PayPal</td>\n", "    </tr>\n", "    <tr>\n", "      <th>236</th>\n", "      <td>10237</td>\n", "      <td>2024-08-24</td>\n", "      <td>Clothing</td>\n", "      <td>Nike Air Force 1 Sneakers</td>\n", "      <td>3</td>\n", "      <td>90.00</td>\n", "      <td>270.00</td>\n", "      <td>Asia</td>\n", "      <td>Debit Card</td>\n", "    </tr>\n", "    <tr>\n", "      <th>237</th>\n", "      <td>10238</td>\n", "      <td>2024-08-25</td>\n", "      <td>Books</td>\n", "      <td>The Handmaid's Tale by <PERSON></td>\n", "      <td>3</td>\n", "      <td>10.99</td>\n", "      <td>32.97</td>\n", "      <td>North America</td>\n", "      <td>Credit Card</td>\n", "    </tr>\n", "    <tr>\n", "      <th>238</th>\n", "      <td>10239</td>\n", "      <td>2024-08-26</td>\n", "      <td>Beauty Products</td>\n", "      <td>Sunday Riley Luna Sleeping Night Oil</td>\n", "      <td>1</td>\n", "      <td>55.00</td>\n", "      <td>55.00</td>\n", "      <td>Europe</td>\n", "      <td>PayPal</td>\n", "    </tr>\n", "    <tr>\n", "      <th>239</th>\n", "      <td>10240</td>\n", "      <td>2024-08-27</td>\n", "      <td>Sports</td>\n", "      <td>Yeti Rambler 20 oz Tumbler</td>\n", "      <td>2</td>\n", "      <td>29.99</td>\n", "      <td>59.98</td>\n", "      <td>Asia</td>\n", "      <td>Credit Card</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Transaction ID        Date Product Category  \\\n", "235           10236  2024-08-23  Home Appliances   \n", "236           10237  2024-08-24         <PERSON><PERSON><PERSON>   \n", "237           10238  2024-08-25            Books   \n", "238           10239  2024-08-26  Beauty Products   \n", "239           10240  2024-08-27           Sports   \n", "\n", "                                        Product Name  Units Sold  Unit Price  \\\n", "235  Nespresso Vertuo Next Coffee and Espresso Maker           1      159.99   \n", "236                        Nike Air Force 1 Sneakers           3       90.00   \n", "237           The Handmaid's Tale by <PERSON>           3       10.99   \n", "238             Sunday Riley Luna Sleeping Night Oil           1       55.00   \n", "239                       Yeti Rambler 20 oz Tumbler           2       29.99   \n", "\n", "     Total Revenue         Region Payment Method  \n", "235         159.99         Europe         PayPal  \n", "236         270.00           Asia     Debit Card  \n", "237          32.97  North America    Credit Card  \n", "238          55.00         Europe         PayPal  \n", "239          59.98           Asia    Credit Card  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["df.tail(5)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Age</th>\n", "      <th>City</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>25</td>\n", "      <td>Bangalore</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>John</td>\n", "      <td>30</td>\n", "      <td>New York</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Jack</td>\n", "      <td>45</td>\n", "      <td>Florida</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Name  Age       City\n", "0  Krish   25  Bangalore\n", "1   John   30   New York\n", "2   Jack   45    Florida"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["### Accessing Data From Dataframe\n", "df"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    Krish\n", "1     John\n", "2     <PERSON>\n", "Name: Name, dtype: object"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["df['Name']"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["Name        <PERSON><PERSON>\n", "Age            25\n", "City    Bangalore\n", "Name: 0, dtype: object"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["df.loc[0]"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["Name        <PERSON><PERSON>\n", "Age            25\n", "City    Bangalore\n", "Name: 0, dtype: object"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["df.il<PERSON>[0]"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Age</th>\n", "      <th>City</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>25</td>\n", "      <td>Bangalore</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>John</td>\n", "      <td>30</td>\n", "      <td>New York</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Jack</td>\n", "      <td>45</td>\n", "      <td>Florida</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Name  Age       City\n", "0  Krish   25  Bangalore\n", "1   John   30   New York\n", "2   Jack   45    Florida"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/plain": ["45"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["## Accessing a specified element\n", "df.at[2,'Age']"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Jack'"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["df.at[2,'Name']"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Florida'"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["## Accessing a specified element using iat\n", "df.iat[2,2]"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Age</th>\n", "      <th>City</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>25</td>\n", "      <td>Bangalore</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>John</td>\n", "      <td>30</td>\n", "      <td>New York</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Jack</td>\n", "      <td>45</td>\n", "      <td>Florida</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Name  Age       City\n", "0  Krish   25  Bangalore\n", "1   John   30   New York\n", "2   Jack   45    Florida"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Age</th>\n", "      <th>City</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>25</td>\n", "      <td>Bangalore</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>John</td>\n", "      <td>30</td>\n", "      <td>New York</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Jack</td>\n", "      <td>45</td>\n", "      <td>Florida</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Name  Age       City\n", "0  Krish   25  Bangalore\n", "1   John   30   New York\n", "2   Jack   45    Florida"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["### Data MAnipulation with Dataframe\n", "df"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Age</th>\n", "      <th>City</th>\n", "      <th><PERSON><PERSON></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>25</td>\n", "      <td>Bangalore</td>\n", "      <td>50000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>John</td>\n", "      <td>30</td>\n", "      <td>New York</td>\n", "      <td>60000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Jack</td>\n", "      <td>45</td>\n", "      <td>Florida</td>\n", "      <td>70000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Name  Age       City  Salary\n", "0  Krish   25  Bangalore   50000\n", "1   <PERSON>   30   New York   60000\n", "2   Jack   45    Florida   70000"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["## Adding a column\n", "df['Salary']=[50000,60000,70000]\n", "df"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["## Remove a column\n", "df.drop('Salary',axis=1,inplace=True)"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Age</th>\n", "      <th>City</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>25</td>\n", "      <td>Bangalore</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>John</td>\n", "      <td>30</td>\n", "      <td>New York</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Jack</td>\n", "      <td>45</td>\n", "      <td>Florida</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Name  Age       City\n", "0  Krish   25  Bangalore\n", "1   John   30   New York\n", "2   Jack   45    Florida"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Age</th>\n", "      <th>City</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>26</td>\n", "      <td>Bangalore</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>John</td>\n", "      <td>31</td>\n", "      <td>New York</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Jack</td>\n", "      <td>46</td>\n", "      <td>Florida</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Name  Age       City\n", "0  Krish   26  Bangalore\n", "1   John   31   New York\n", "2   Jack   46    Florida"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["## Add age to the column\n", "df['Age']=df['Age']+1\n", "df"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [], "source": ["df.drop(0,inplace=True)"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Age</th>\n", "      <th>City</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>John</td>\n", "      <td>31</td>\n", "      <td>New York</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Jack</td>\n", "      <td>46</td>\n", "      <td>Florida</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Name  Age      City\n", "1  John   31  New York\n", "2  Jack   46   Florida"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Transaction ID</th>\n", "      <th>Date</th>\n", "      <th>Product Category</th>\n", "      <th>Product Name</th>\n", "      <th>Units Sold</th>\n", "      <th>Unit Price</th>\n", "      <th>Total Revenue</th>\n", "      <th>Region</th>\n", "      <th>Payment Method</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10001</td>\n", "      <td>2024-01-01</td>\n", "      <td>Electronics</td>\n", "      <td>iPhone 14 Pro</td>\n", "      <td>2</td>\n", "      <td>999.99</td>\n", "      <td>1999.98</td>\n", "      <td>North America</td>\n", "      <td>Credit Card</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10002</td>\n", "      <td>2024-01-02</td>\n", "      <td>Home Appliances</td>\n", "      <td>Dyson V11 Vacuum</td>\n", "      <td>1</td>\n", "      <td>499.99</td>\n", "      <td>499.99</td>\n", "      <td>Europe</td>\n", "      <td>PayPal</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>10003</td>\n", "      <td>2024-01-03</td>\n", "      <td>Clothing</td>\n", "      <td>Levi's 501 Jeans</td>\n", "      <td>3</td>\n", "      <td>69.99</td>\n", "      <td>209.97</td>\n", "      <td>Asia</td>\n", "      <td>Debit Card</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>10004</td>\n", "      <td>2024-01-04</td>\n", "      <td>Books</td>\n", "      <td>The Da Vinci Code</td>\n", "      <td>4</td>\n", "      <td>15.99</td>\n", "      <td>63.96</td>\n", "      <td>North America</td>\n", "      <td>Credit Card</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>10005</td>\n", "      <td>2024-01-05</td>\n", "      <td>Beauty Products</td>\n", "      <td>Neutrogena Skincare Set</td>\n", "      <td>1</td>\n", "      <td>89.99</td>\n", "      <td>89.99</td>\n", "      <td>Europe</td>\n", "      <td>PayPal</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Transaction ID        Date Product Category             Product Name  \\\n", "0           10001  2024-01-01      Electronics            iPhone 14 Pro   \n", "1           10002  2024-01-02  Home Appliances         Dyson V11 Vacuum   \n", "2           10003  2024-01-03         <PERSON><PERSON><PERSON>'s 501 Jeans   \n", "3           10004  2024-01-04            Books        The Da Vinci Code   \n", "4           10005  2024-01-05  Beauty Products  Neutrogena Skincare Set   \n", "\n", "   Units Sold  Unit Price  Total Revenue         Region Payment Method  \n", "0           2      999.99        1999.98  North America    Credit Card  \n", "1           1      499.99         499.99         Europe         PayPal  \n", "2           3       69.99         209.97           Asia     Debit Card  \n", "3           4       15.99          63.96  North America    Credit Card  \n", "4           1       89.99          89.99         Europe         PayPal  "]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["df=pd.read_csv('sales_data.csv')\n", "df.head(5)"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data types:\n", " Transaction ID        int64\n", "Date                 object\n", "Product Category     object\n", "Product Name         object\n", "Units Sold            int64\n", "Unit Price          float64\n", "Total Revenue       float64\n", "Region               object\n", "Payment Method       object\n", "dtype: object\n", "Statistical summary:\n", "        Transaction ID  Units Sold   Unit Price  Total Revenue\n", "count       240.00000  240.000000   240.000000     240.000000\n", "mean      10120.50000    2.158333   236.395583     335.699375\n", "std          69.42622    1.322454   429.446695     485.804469\n", "min       10001.00000    1.000000     6.500000       6.500000\n", "25%       10060.75000    1.000000    29.500000      62.965000\n", "50%       10120.50000    2.000000    89.990000     179.970000\n", "75%       10180.25000    3.000000   249.990000     399.225000\n", "max       10240.00000   10.000000  3899.990000    3899.990000\n"]}], "source": ["# Display the data types of each column\n", "print(\"Data types:\\n\", df.dtypes)\n", "\n", "# Describe the DataFrame\n", "print(\"Statistical summary:\\n\", df.describe())\n", "\n"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Transaction ID</th>\n", "      <th>Units Sold</th>\n", "      <th>Unit Price</th>\n", "      <th>Total Revenue</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>240.00000</td>\n", "      <td>240.000000</td>\n", "      <td>240.000000</td>\n", "      <td>240.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>10120.50000</td>\n", "      <td>2.158333</td>\n", "      <td>236.395583</td>\n", "      <td>335.699375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>69.42622</td>\n", "      <td>1.322454</td>\n", "      <td>429.446695</td>\n", "      <td>485.804469</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>10001.00000</td>\n", "      <td>1.000000</td>\n", "      <td>6.500000</td>\n", "      <td>6.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>10060.75000</td>\n", "      <td>1.000000</td>\n", "      <td>29.500000</td>\n", "      <td>62.965000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>10120.50000</td>\n", "      <td>2.000000</td>\n", "      <td>89.990000</td>\n", "      <td>179.970000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>10180.25000</td>\n", "      <td>3.000000</td>\n", "      <td>249.990000</td>\n", "      <td>399.225000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>10240.00000</td>\n", "      <td>10.000000</td>\n", "      <td>3899.990000</td>\n", "      <td>3899.990000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Transaction ID  Units Sold   Unit Price  Total Revenue\n", "count       240.00000  240.000000   240.000000     240.000000\n", "mean      10120.50000    2.158333   236.395583     335.699375\n", "std          69.42622    1.322454   429.446695     485.804469\n", "min       10001.00000    1.000000     6.500000       6.500000\n", "25%       10060.75000    1.000000    29.500000      62.965000\n", "50%       10120.50000    2.000000    89.990000     179.970000\n", "75%       10180.25000    3.000000   249.990000     399.225000\n", "max       10240.00000   10.000000  3899.990000    3899.990000"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["df.describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}