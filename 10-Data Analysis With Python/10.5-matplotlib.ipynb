{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### Data Visualization With Matplotlib\n", "\n", "Matplotlib is a powerful plotting library for Python that enables the creation of static, animated, and interactive visualizations. It is widely used for data visualization in data science and analytics. In this lesson, we will cover the basics of Matplotlib, including creating various types of plots and customizing them."]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: matplotlib in e:\\udemy final\\python\\venv\\lib\\site-packages (3.9.0)\n", "Requirement already satisfied: contourpy>=1.0.1 in e:\\udemy final\\python\\venv\\lib\\site-packages (from matplotlib) (1.2.1)\n", "Requirement already satisfied: cycler>=0.10 in e:\\udemy final\\python\\venv\\lib\\site-packages (from matplotlib) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in e:\\udemy final\\python\\venv\\lib\\site-packages (from matplotlib) (4.53.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in e:\\udemy final\\python\\venv\\lib\\site-packages (from matplotlib) (1.4.5)\n", "Requirement already satisfied: numpy>=1.23 in e:\\udemy final\\python\\venv\\lib\\site-packages (from matplotlib) (1.26.4)\n", "Requirement already satisfied: packaging>=20.0 in e:\\udemy final\\python\\venv\\lib\\site-packages (from matplotlib) (24.0)\n", "Requirement already satisfied: pillow>=8 in e:\\udemy final\\python\\venv\\lib\\site-packages (from matplotlib) (10.3.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in e:\\udemy final\\python\\venv\\lib\\site-packages (from matplotlib) (3.1.2)\n", "Requirement already satisfied: python-dateutil>=2.7 in e:\\udemy final\\python\\venv\\lib\\site-packages (from matplotlib) (2.9.0.post0)\n", "Requirement already satisfied: six>=1.5 in e:\\udemy final\\python\\venv\\lib\\site-packages (from python-dateutil>=2.7->matplotlib) (1.16.0)\n"]}], "source": ["!pip install matplotlib"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["x=[1,2,3,4,5]\n", "y=[1,4,9,16,25]\n", "\n", "##create a line plot\n", "plt.plot(x,y)\n", "plt.xlabel('X axis')\n", "plt.ylabel('Y Axis')\n", "plt.title(\"Basic Line Plot\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["x=[1,2,3,4,5]\n", "y=[1,4,9,16,25]\n", "\n", "##create a customized line plot\n", "\n", "plt.plot(x,y,color='red',linestyle='--',marker='o',linewidth=3,markersize=9)\n", "plt.grid(True)\n"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0.5, 1.0, 'Plot 4')"]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 900x500 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["## Multiple Plots\n", "## Sample data\n", "x = [1, 2, 3, 4, 5]\n", "y1 = [1, 4, 9, 16, 25]\n", "y2 = [1, 2, 3, 4, 5]\n", "\n", "plt.figure(figsize=(9,5))\n", "\n", "plt.subplot(2,2,1)\n", "plt.plot(x,y1,color='green')\n", "plt.title(\"Plot 1\")\n", "\n", "plt.subplot(2,2,2)\n", "plt.plot(y1,x,color='red')\n", "plt.title(\"Plot 2\")\n", "\n", "plt.subplot(2,2,3)\n", "plt.plot(x,y2,color='blue')\n", "plt.title(\"Plot 3\")\n", "\n", "plt.subplot(2,2,4)\n", "plt.plot(x,y2,color='green')\n", "plt.title(\"Plot 4\")\n"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["###<PERSON> Plor\n", "categories=['A','B','C','D','E']\n", "values=[5,7,3,8,6]\n", "\n", "##create a bar plot\n", "plt.bar(categories,values,color='purple')\n", "plt.xlabel('Categories')\n", "plt.ylabel('Values')\n", "plt.title('Bar Plot')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Histograms\n", "Histograms are used to represent the distribution of a dataset. They divide the data into bins and count the number of data points in each bin."]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [{"data": {"text/plain": ["(array([1., 2., 3., 4., 5.]),\n", " array([1. , 1.8, 2.6, 3.4, 4.2, 5. ]),\n", " <BarContainer object of 5 artists>)"]}, "execution_count": 76, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Sample data\n", "data = [1, 2, 2, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 5]\n", "\n", "##create a histogram\n", "plt.hist(data,bins=5,color='orange',edgecolor='black')"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.collections.PathCollection at 0x25699a6b080>"]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["##create a scatter plot\n", "# Sample data\n", "x = [1, 2, 3, 4, 5]\n", "y = [2, 3, 4, 5, 6]\n", "\n", "plt.scatter(x,y,color=\"blue\",marker='x')"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [{"data": {"text/plain": ["([<matplotlib.patches.Wedge at 0x2569ea3ca10>,\n", "  <matplotlib.patches.Wedge at 0x2569ea3c8f0>,\n", "  <matplotlib.patches.Wedge at 0x2569ea3d4f0>,\n", "  <matplotlib.patches.Wedge at 0x2569ea3dbb0>],\n", " [Text(0.764120788592483, 1.051722121304293, 'A'),\n", "  Text(-0.8899187482945419, 0.6465637025335369, 'B'),\n", "  Text(-0.3399185762739153, -1.046162206115244, 'C'),\n", "  Text(1.0461622140716127, -0.3399185517867209, 'D')],\n", " [Text(0.47022817759537416, 0.6472136131103341, '30.0%'),\n", "  Text(-0.4854102263424773, 0.3526711104728383, '20.0%'),\n", "  Text(-0.1854101325130447, -0.5706339306083149, '40.0%'),\n", "  Text(0.5706339349481523, -0.18541011915639322, '10.0%')])"]}, "execution_count": 83, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["### pie chart\n", "\n", "labels=['A','B','C','D']\n", "sizes=[30,20,40,10]\n", "colors=['gold','yellowgreen','lightcoral','lightskyblue']\n", "explode=(0.2,0,0,0) ##move out the 1st slice\n", "\n", "##create apie chart\n", "plt.pie(sizes,explode=explode,labels=labels,colors=colors,autopct=\"%1.1f%%\",shadow=True)"]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Transaction ID</th>\n", "      <th>Date</th>\n", "      <th>Product Category</th>\n", "      <th>Product Name</th>\n", "      <th>Units Sold</th>\n", "      <th>Unit Price</th>\n", "      <th>Total Revenue</th>\n", "      <th>Region</th>\n", "      <th>Payment Method</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10001</td>\n", "      <td>2024-01-01</td>\n", "      <td>Electronics</td>\n", "      <td>iPhone 14 Pro</td>\n", "      <td>2</td>\n", "      <td>999.99</td>\n", "      <td>1999.98</td>\n", "      <td>North America</td>\n", "      <td>Credit Card</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10002</td>\n", "      <td>2024-01-02</td>\n", "      <td>Home Appliances</td>\n", "      <td>Dyson V11 Vacuum</td>\n", "      <td>1</td>\n", "      <td>499.99</td>\n", "      <td>499.99</td>\n", "      <td>Europe</td>\n", "      <td>PayPal</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>10003</td>\n", "      <td>2024-01-03</td>\n", "      <td>Clothing</td>\n", "      <td>Levi's 501 Jeans</td>\n", "      <td>3</td>\n", "      <td>69.99</td>\n", "      <td>209.97</td>\n", "      <td>Asia</td>\n", "      <td>Debit Card</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>10004</td>\n", "      <td>2024-01-04</td>\n", "      <td>Books</td>\n", "      <td>The Da Vinci Code</td>\n", "      <td>4</td>\n", "      <td>15.99</td>\n", "      <td>63.96</td>\n", "      <td>North America</td>\n", "      <td>Credit Card</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>10005</td>\n", "      <td>2024-01-05</td>\n", "      <td>Beauty Products</td>\n", "      <td>Neutrogena Skincare Set</td>\n", "      <td>1</td>\n", "      <td>89.99</td>\n", "      <td>89.99</td>\n", "      <td>Europe</td>\n", "      <td>PayPal</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Transaction ID        Date Product Category             Product Name  \\\n", "0           10001  2024-01-01      Electronics            iPhone 14 Pro   \n", "1           10002  2024-01-02  Home Appliances         Dyson V11 Vacuum   \n", "2           10003  2024-01-03         <PERSON><PERSON><PERSON>'s 501 Jeans   \n", "3           10004  2024-01-04            Books        The Da Vinci Code   \n", "4           10005  2024-01-05  Beauty Products  Neutrogena Skincare Set   \n", "\n", "   Units Sold  Unit Price  Total Revenue         Region Payment Method  \n", "0           2      999.99        1999.98  North America    Credit Card  \n", "1           1      499.99         499.99         Europe         PayPal  \n", "2           3       69.99         209.97           Asia     Debit Card  \n", "3           4       15.99          63.96  North America    Credit Card  \n", "4           1       89.99          89.99         Europe         PayPal  "]}, "execution_count": 87, "metadata": {}, "output_type": "execute_result"}], "source": ["## Sales Data Visualization\n", "import pandas as pd\n", "sales_data_df=pd.read_csv('sales_data.csv')\n", "sales_data_df.head(5)"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 240 entries, 0 to 239\n", "Data columns (total 9 columns):\n", " #   Column            Non-Null Count  Dtype  \n", "---  ------            --------------  -----  \n", " 0   Transaction ID    240 non-null    int64  \n", " 1   Date              240 non-null    object \n", " 2   Product Category  240 non-null    object \n", " 3   Product Name      240 non-null    object \n", " 4   Units Sold        240 non-null    int64  \n", " 5   Unit Price        240 non-null    float64\n", " 6   Total Revenue     240 non-null    float64\n", " 7   Region            240 non-null    object \n", " 8   Payment Method    240 non-null    object \n", "dtypes: float64(2), int64(2), object(5)\n", "memory usage: 17.0+ KB\n"]}], "source": ["sales_data_df.info()"]}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Product Category\n", "Beauty Products     2621.90\n", "Books               1861.93\n", "Clothing            8128.93\n", "Electronics        34982.41\n", "Home Appliances    18646.16\n", "Sports             14326.52\n", "Name: Total Revenue, dtype: float64\n"]}], "source": ["## plot total sales by products\n", "total_sales_by_product=sales_data_df.groupby('Product Category')['Total Revenue'].sum()\n", "print(total_sales_by_product)"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='Product Category'>"]}, "execution_count": 90, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["total_sales_by_product.plot(kind='bar',color='teal')"]}, {"cell_type": "code", "execution_count": 94, "metadata": {}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x2569e9b46e0>]"]}, "execution_count": 94, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["## plot sales trend over time\n", "sales_trend=sales_data_df.groupby('Date')['Total Revenue'].sum().reset_index()\n", "plt.plot(sales_trend['Date'],sales_trend['Total Revenue'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}