#!/usr/bin/env python
# coding: utf-8

# #### Data Visualization With Matplotlib
# 
# Matplotlib is a powerful plotting library for Python that enables the creation of static, animated, and interactive visualizations. It is widely used for data visualization in data science and analytics. In this lesson, we will cover the basics of Matplotlib, including creating various types of plots and customizing them.

# In[42]:


get_ipython().system('pip install matplotlib')


# In[43]:


import matplotlib.pyplot as plt


# In[45]:


x=[1,2,3,4,5]
y=[1,4,9,16,25]

##create a line plot
plt.plot(x,y)
plt.xlabel('X axis')
plt.ylabel('Y Axis')
plt.title("Basic Line Plot")
plt.show()


# In[57]:


x=[1,2,3,4,5]
y=[1,4,9,16,25]

##create a customized line plot

plt.plot(x,y,color='red',linestyle='--',marker='o',linewidth=3,markersize=9)
plt.grid(True)


# In[72]:


## Multiple Plots
## Sample data
x = [1, 2, 3, 4, 5]
y1 = [1, 4, 9, 16, 25]
y2 = [1, 2, 3, 4, 5]

plt.figure(figsize=(9,5))

plt.subplot(2,2,1)
plt.plot(x,y1,color='green')
plt.title("Plot 1")

plt.subplot(2,2,2)
plt.plot(y1,x,color='red')
plt.title("Plot 2")

plt.subplot(2,2,3)
plt.plot(x,y2,color='blue')
plt.title("Plot 3")

plt.subplot(2,2,4)
plt.plot(x,y2,color='green')
plt.title("Plot 4")


# In[74]:


###Bar Plor
categories=['A','B','C','D','E']
values=[5,7,3,8,6]

##create a bar plot
plt.bar(categories,values,color='purple')
plt.xlabel('Categories')
plt.ylabel('Values')
plt.title('Bar Plot')
plt.show()


# #### Histograms
# Histograms are used to represent the distribution of a dataset. They divide the data into bins and count the number of data points in each bin.

# In[76]:


# Sample data
data = [1, 2, 2, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 5]

##create a histogram
plt.hist(data,bins=5,color='orange',edgecolor='black')


# In[78]:


##create a scatter plot
# Sample data
x = [1, 2, 3, 4, 5]
y = [2, 3, 4, 5, 6]

plt.scatter(x,y,color="blue",marker='x')


# In[83]:


### pie chart

labels=['A','B','C','D']
sizes=[30,20,40,10]
colors=['gold','yellowgreen','lightcoral','lightskyblue']
explode=(0.2,0,0,0) ##move out the 1st slice

##create apie chart
plt.pie(sizes,explode=explode,labels=labels,colors=colors,autopct="%1.1f%%",shadow=True)


# In[87]:


## Sales Data Visualization
import pandas as pd
sales_data_df=pd.read_csv('sales_data.csv')
sales_data_df.head(5)


# In[88]:


sales_data_df.info()


# In[89]:


## plot total sales by products
total_sales_by_product=sales_data_df.groupby('Product Category')['Total Revenue'].sum()
print(total_sales_by_product)


# In[90]:


total_sales_by_product.plot(kind='bar',color='teal')


# In[94]:


## plot sales trend over time
sales_trend=sales_data_df.groupby('Date')['Total Revenue'].sum().reset_index()
plt.plot(sales_trend['Date'],sales_trend['Total Revenue'])


# In[ ]:





# In[ ]:




