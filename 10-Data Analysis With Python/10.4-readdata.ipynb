{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Reading Data From Different Sources\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from io import StringIO\n", "Data = '{\"employee_name\": \"<PERSON>\", \"email\": \"<EMAIL>\", \"job_profile\": [{\"title1\":\"Team Lead\", \"title2\":\"<PERSON><PERSON> Dev<PERSON>\"}]}'\n", "df=pd.read_json(StringIO(Data))"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>employee_name</th>\n", "      <th>email</th>\n", "      <th>job_profile</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON></td>\n", "      <td><EMAIL></td>\n", "      <td>{'title1': 'Team Lead', 'title2': '<PERSON><PERSON>...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  employee_name            email  \\\n", "0         <PERSON>  <EMAIL>   \n", "\n", "                                         job_profile  \n", "0  {'title1': 'Team Lead', 'title2': '<PERSON><PERSON>...  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["'{\"employee_name\":{\"0\":\"<PERSON>\"},\"email\":{\"0\":\"<EMAIL>\"},\"job_profile\":{\"0\":{\"title1\":\"Team Lead\",\"title2\":\"Sr. Developer\"}}}'"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df.to_json()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["'{\"0\":{\"employee_name\":\"<PERSON>\",\"email\":\"<EMAIL>\",\"job_profile\":{\"title1\":\"Team Lead\",\"title2\":\"<PERSON><PERSON> Developer\"}}}'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df.to_json(orient='index')"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["'[{\"employee_name\":\"<PERSON>\",\"email\":\"<EMAIL>\",\"job_profile\":{\"title1\":\"Team Lead\",\"title2\":\"<PERSON><PERSON> Developer\"}}]'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df.to_json(orient='records')"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["df=pd.read_csv(\"https://archive.ics.uci.edu/ml/machine-learning-databases/wine/wine.data\",header=None)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "      <th>4</th>\n", "      <th>5</th>\n", "      <th>6</th>\n", "      <th>7</th>\n", "      <th>8</th>\n", "      <th>9</th>\n", "      <th>10</th>\n", "      <th>11</th>\n", "      <th>12</th>\n", "      <th>13</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>14.23</td>\n", "      <td>1.71</td>\n", "      <td>2.43</td>\n", "      <td>15.6</td>\n", "      <td>127</td>\n", "      <td>2.80</td>\n", "      <td>3.06</td>\n", "      <td>0.28</td>\n", "      <td>2.29</td>\n", "      <td>5.64</td>\n", "      <td>1.04</td>\n", "      <td>3.92</td>\n", "      <td>1065</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>13.20</td>\n", "      <td>1.78</td>\n", "      <td>2.14</td>\n", "      <td>11.2</td>\n", "      <td>100</td>\n", "      <td>2.65</td>\n", "      <td>2.76</td>\n", "      <td>0.26</td>\n", "      <td>1.28</td>\n", "      <td>4.38</td>\n", "      <td>1.05</td>\n", "      <td>3.40</td>\n", "      <td>1050</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>13.16</td>\n", "      <td>2.36</td>\n", "      <td>2.67</td>\n", "      <td>18.6</td>\n", "      <td>101</td>\n", "      <td>2.80</td>\n", "      <td>3.24</td>\n", "      <td>0.30</td>\n", "      <td>2.81</td>\n", "      <td>5.68</td>\n", "      <td>1.03</td>\n", "      <td>3.17</td>\n", "      <td>1185</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>14.37</td>\n", "      <td>1.95</td>\n", "      <td>2.50</td>\n", "      <td>16.8</td>\n", "      <td>113</td>\n", "      <td>3.85</td>\n", "      <td>3.49</td>\n", "      <td>0.24</td>\n", "      <td>2.18</td>\n", "      <td>7.80</td>\n", "      <td>0.86</td>\n", "      <td>3.45</td>\n", "      <td>1480</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>13.24</td>\n", "      <td>2.59</td>\n", "      <td>2.87</td>\n", "      <td>21.0</td>\n", "      <td>118</td>\n", "      <td>2.80</td>\n", "      <td>2.69</td>\n", "      <td>0.39</td>\n", "      <td>1.82</td>\n", "      <td>4.32</td>\n", "      <td>1.04</td>\n", "      <td>2.93</td>\n", "      <td>735</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   0      1     2     3     4    5     6     7     8     9     10    11    12  \\\n", "0   1  14.23  1.71  2.43  15.6  127  2.80  3.06  0.28  2.29  5.64  1.04  3.92   \n", "1   1  13.20  1.78  2.14  11.2  100  2.65  2.76  0.26  1.28  4.38  1.05  3.40   \n", "2   1  13.16  2.36  2.67  18.6  101  2.80  3.24  0.30  2.81  5.68  1.03  3.17   \n", "3   1  14.37  1.95  2.50  16.8  113  3.85  3.49  0.24  2.18  7.80  0.86  3.45   \n", "4   1  13.24  2.59  2.87  21.0  118  2.80  2.69  0.39  1.82  4.32  1.04  2.93   \n", "\n", "     13  \n", "0  1065  \n", "1  1050  \n", "2  1185  \n", "3  1480  \n", "4   735  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["df.to_csv(\"wine.csv\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting lxml\n", "  Downloading lxml-5.2.2-cp312-cp312-win_amd64.whl.metadata (3.5 kB)\n", "Downloading lxml-5.2.2-cp312-cp312-win_amd64.whl (3.8 MB)\n", "   ---------------------------------------- 0.0/3.8 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/3.8 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/3.8 MB 660.6 kB/s eta 0:00:06\n", "   ---------------------------------------- 0.0/3.8 MB 660.6 kB/s eta 0:00:06\n", "   ---------------------------------------- 0.0/3.8 MB 660.6 kB/s eta 0:00:06\n", "   - -------------------------------------- 0.2/3.8 MB 756.6 kB/s eta 0:00:05\n", "   ---- ----------------------------------- 0.4/3.8 MB 1.8 MB/s eta 0:00:02\n", "   -------- ------------------------------- 0.8/3.8 MB 2.8 MB/s eta 0:00:02\n", "   -------------- ------------------------- 1.3/3.8 MB 3.9 MB/s eta 0:00:01\n", "   --------------- ------------------------ 1.4/3.8 MB 4.1 MB/s eta 0:00:01\n", "   --------------- ------------------------ 1.4/3.8 MB 4.1 MB/s eta 0:00:01\n", "   ---------------- ----------------------- 1.5/3.8 MB 3.3 MB/s eta 0:00:01\n", "   ------------------ --------------------- 1.8/3.8 MB 3.3 MB/s eta 0:00:01\n", "   ---------------------- ----------------- 2.1/3.8 MB 3.6 MB/s eta 0:00:01\n", "   -------------------------- ------------- 2.5/3.8 MB 4.0 MB/s eta 0:00:01\n", "   ---------------------------- ----------- 2.7/3.8 MB 4.2 MB/s eta 0:00:01\n", "   ---------------------------- ----------- 2.7/3.8 MB 4.2 MB/s eta 0:00:01\n", "   ---------------------------- ----------- 2.7/3.8 MB 4.2 MB/s eta 0:00:01\n", "   ------------------------------ --------- 2.9/3.8 MB 3.5 MB/s eta 0:00:01\n", "   --------------------------------- ------ 3.2/3.8 MB 3.7 MB/s eta 0:00:01\n", "   ------------------------------------ --- 3.5/3.8 MB 3.9 MB/s eta 0:00:01\n", "   ---------------------------------------  3.8/3.8 MB 4.1 MB/s eta 0:00:01\n", "   ---------------------------------------- 3.8/3.8 MB 4.0 MB/s eta 0:00:00\n", "Installing collected packages: lxml\n", "Successfully installed lxml-5.2.2\n"]}], "source": ["!pip install lxml"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: html5lib in e:\\udemy final\\python\\venv\\lib\\site-packages (1.1)\n", "Requirement already satisfied: six>=1.9 in e:\\udemy final\\python\\venv\\lib\\site-packages (from html5lib) (1.16.0)\n", "Requirement already satisfied: webencodings in e:\\udemy final\\python\\venv\\lib\\site-packages (from html5lib) (0.5.1)\n", "Collecting beautifulsoup4\n", "  Using cached beautifulsoup4-4.12.3-py3-none-any.whl.metadata (3.8 kB)\n", "Collecting soupsieve>1.2 (from beautifulsoup4)\n", "  Using cached soupsieve-2.5-py3-none-any.whl.metadata (4.7 kB)\n", "Using cached beautifulsoup4-4.12.3-py3-none-any.whl (147 kB)\n", "Using cached soupsieve-2.5-py3-none-any.whl (36 kB)\n", "Installing collected packages: soupsieve, beautifulsoup4\n", "Successfully installed beautifulsoup4-4.12.3 soupsieve-2.5\n"]}], "source": ["!pip install html5lib\n", "!pip install beautifulsoup4"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["url=\"https://www.fdic.gov/resources/resolutions/bank-failures/failed-bank-list/\"\n", "\n", "df=pd.read_html(url)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Bank NameBank</th>\n", "      <th>CityCity</th>\n", "      <th>StateSt</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>Acquiring InstitutionAI</th>\n", "      <th>Closing DateClosing</th>\n", "      <th>FundFund</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Republic First Bank dba Republic Bank</td>\n", "      <td>Philadelphia</td>\n", "      <td>PA</td>\n", "      <td>27332</td>\n", "      <td>Fulton Bank, National Association</td>\n", "      <td>April 26, 2024</td>\n", "      <td>10546</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Citizens Bank</td>\n", "      <td>Sac City</td>\n", "      <td>IA</td>\n", "      <td>8758</td>\n", "      <td>Iowa Trust &amp; Savings Bank</td>\n", "      <td>November 3, 2023</td>\n", "      <td>10545</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Heartland Tri-State Bank</td>\n", "      <td>Elkhart</td>\n", "      <td>KS</td>\n", "      <td>25851</td>\n", "      <td>Dream First Bank, N.A.</td>\n", "      <td>July 28, 2023</td>\n", "      <td>10544</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>First Republic Bank</td>\n", "      <td>San Francisco</td>\n", "      <td>CA</td>\n", "      <td>59017</td>\n", "      <td>JPMorgan Chase Bank, N.A.</td>\n", "      <td>May 1, 2023</td>\n", "      <td>10543</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Signature Bank</td>\n", "      <td>New York</td>\n", "      <td>NY</td>\n", "      <td>57053</td>\n", "      <td>Flagstar Bank, N.A.</td>\n", "      <td>March 12, 2023</td>\n", "      <td>10540</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>564</th>\n", "      <td>Superior Bank, FSB</td>\n", "      <td>Hinsdale</td>\n", "      <td>IL</td>\n", "      <td>32646</td>\n", "      <td>Superior Federal, FSB</td>\n", "      <td>July 27, 2001</td>\n", "      <td>6004</td>\n", "    </tr>\n", "    <tr>\n", "      <th>565</th>\n", "      <td>Malta National Bank</td>\n", "      <td>Malta</td>\n", "      <td>OH</td>\n", "      <td>6629</td>\n", "      <td>North Valley Bank</td>\n", "      <td>May 3, 2001</td>\n", "      <td>4648</td>\n", "    </tr>\n", "    <tr>\n", "      <th>566</th>\n", "      <td>First Alliance Bank &amp; Trust Co.</td>\n", "      <td>Manchester</td>\n", "      <td>NH</td>\n", "      <td>34264</td>\n", "      <td>Southern New Hampshire Bank &amp; Trust</td>\n", "      <td>February 2, 2001</td>\n", "      <td>4647</td>\n", "    </tr>\n", "    <tr>\n", "      <th>567</th>\n", "      <td>National State Bank of Metropolis</td>\n", "      <td>Metropolis</td>\n", "      <td>IL</td>\n", "      <td>3815</td>\n", "      <td>Banterra Bank of Marion</td>\n", "      <td>December 14, 2000</td>\n", "      <td>4646</td>\n", "    </tr>\n", "    <tr>\n", "      <th>568</th>\n", "      <td>Bank of Honolulu</td>\n", "      <td>Honolulu</td>\n", "      <td>HI</td>\n", "      <td>21029</td>\n", "      <td>Bank of the Orient</td>\n", "      <td>October 13, 2000</td>\n", "      <td>4645</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>569 rows × 7 columns</p>\n", "</div>"], "text/plain": ["                             Bank NameBank       CityCity StateSt  CertCert  \\\n", "0    Republic First Bank dba Republic Bank   Philadelphia      PA     27332   \n", "1                            Citizens Bank       Sac City      IA      8758   \n", "2                 Heartland Tri-State Bank        Elkhart      KS     25851   \n", "3                      First Republic Bank  San Francisco      CA     59017   \n", "4                           Signature Bank       New York      NY     57053   \n", "..                                     ...            ...     ...       ...   \n", "564                     Superior Bank, FSB       Hinsdale      IL     32646   \n", "565                    Malta National Bank          Malta      OH      6629   \n", "566        First Alliance Bank & Trust Co.     Manchester      NH     34264   \n", "567      National State Bank of Metropolis     Metropolis      IL      3815   \n", "568                       Bank of Honolulu       Honolulu      HI     21029   \n", "\n", "                 Acquiring InstitutionAI Closing DateClosing  FundFund  \n", "0      Fulton Bank, National Association      April 26, 2024     10546  \n", "1              Iowa Trust & Savings Bank    November 3, 2023     10545  \n", "2                 Dream First Bank, N.A.       July 28, 2023     10544  \n", "3              JPMorgan Chase Bank, N.A.         May 1, 2023     10543  \n", "4                    Flagstar Bank, N.A.      March 12, 2023     10540  \n", "..                                   ...                 ...       ...  \n", "564                Superior Federal, FSB       July 27, 2001      6004  \n", "565                    North Valley Bank         May 3, 2001      4648  \n", "566  Southern New Hampshire Bank & Trust    February 2, 2001      4647  \n", "567              Banterra Bank of Marion   December 14, 2000      4646  \n", "568                   Bank of the Orient    October 13, 2000      4645  \n", "\n", "[569 rows x 7 columns]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["df[0]"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Mobile country code</th>\n", "      <th>Country</th>\n", "      <th>ISO 3166</th>\n", "      <th>Mobile network codes</th>\n", "      <th>National MNC authority</th>\n", "      <th>Remarks</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>289</td>\n", "      <td>A Abkhazia</td>\n", "      <td>GE-AB</td>\n", "      <td>List of mobile network codes in Abkhazia</td>\n", "      <td>NaN</td>\n", "      <td>MCC is not listed by ITU</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>412</td>\n", "      <td>Afghanistan</td>\n", "      <td>AF</td>\n", "      <td>List of mobile network codes in Afghanistan</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>276</td>\n", "      <td>Albania</td>\n", "      <td>AL</td>\n", "      <td>List of mobile network codes in Albania</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>603</td>\n", "      <td>Algeria</td>\n", "      <td>DZ</td>\n", "      <td>List of mobile network codes in Algeria</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>544</td>\n", "      <td>American Samoa (United States of America)</td>\n", "      <td>AS</td>\n", "      <td>List of mobile network codes in American Samoa</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>247</th>\n", "      <td>452</td>\n", "      <td>Vietnam</td>\n", "      <td>VN</td>\n", "      <td>List of mobile network codes in the Vietnam</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>248</th>\n", "      <td>543</td>\n", "      <td><PERSON> and <PERSON></td>\n", "      <td>WF</td>\n", "      <td>List of mobile network codes in Wallis and Futuna</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>249</th>\n", "      <td>421</td>\n", "      <td>Y Yemen</td>\n", "      <td>YE</td>\n", "      <td>List of mobile network codes in the Yemen</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>250</th>\n", "      <td>645</td>\n", "      <td>Z Zambia</td>\n", "      <td>ZM</td>\n", "      <td>List of mobile network codes in Zambia</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>251</th>\n", "      <td>648</td>\n", "      <td>Zimbabwe</td>\n", "      <td>ZW</td>\n", "      <td>List of mobile network codes in Zimbabwe</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>252 rows × 6 columns</p>\n", "</div>"], "text/plain": ["     Mobile country code                                    Country ISO 3166  \\\n", "0                    289                                 A Abkhazia    GE-AB   \n", "1                    412                                Afghanistan       AF   \n", "2                    276                                    Albania       AL   \n", "3                    603                                    Algeria       DZ   \n", "4                    544  American Samoa (United States of America)       AS   \n", "..                   ...                                        ...      ...   \n", "247                  452                                    Vietnam       VN   \n", "248                  543                        W Wallis and Futuna       WF   \n", "249                  421                                    Y Yemen       YE   \n", "250                  645                                   Z Zambia       ZM   \n", "251                  648                                   Zimbabwe       ZW   \n", "\n", "                                  Mobile network codes National MNC authority  \\\n", "0             List of mobile network codes in Abkhazia                    NaN   \n", "1          List of mobile network codes in Afghanistan                    NaN   \n", "2              List of mobile network codes in Albania                    NaN   \n", "3              List of mobile network codes in Algeria                    NaN   \n", "4       List of mobile network codes in American Samoa                    NaN   \n", "..                                                 ...                    ...   \n", "247        List of mobile network codes in the Vietnam                    NaN   \n", "248  List of mobile network codes in Wallis and Futuna                    NaN   \n", "249          List of mobile network codes in the Yemen                    NaN   \n", "250             List of mobile network codes in Zambia                    NaN   \n", "251           List of mobile network codes in Zimbabwe                    NaN   \n", "\n", "                      Remarks  \n", "0    MCC is not listed by ITU  \n", "1                         NaN  \n", "2                         NaN  \n", "3                         NaN  \n", "4                         NaN  \n", "..                        ...  \n", "247                       NaN  \n", "248                       NaN  \n", "249                       NaN  \n", "250                       NaN  \n", "251                       NaN  \n", "\n", "[252 rows x 6 columns]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["url=\"https://en.wikipedia.org/wiki/Mobile_country_code\"\n", "pd.read_html(url,match=\"Country\",header=0)[0]"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting openpyxl\n", "  Downloading openpyxl-3.1.4-py2.py3-none-any.whl.metadata (2.5 kB)\n", "Collecting et-xmlfile (from openpyxl)\n", "  Downloading et_xmlfile-1.1.0-py3-none-any.whl.metadata (1.8 kB)\n", "Downloading openpyxl-3.1.4-py2.py3-none-any.whl (251 kB)\n", "   ---------------------------------------- 0.0/251.4 kB ? eta -:--:--\n", "   - -------------------------------------- 10.2/251.4 kB ? eta -:--:--\n", "   ---- ---------------------------------- 30.7/251.4 kB 660.6 kB/s eta 0:00:01\n", "   ---- ---------------------------------- 30.7/251.4 kB 660.6 kB/s eta 0:00:01\n", "   ---- ---------------------------------- 30.7/251.4 kB 660.6 kB/s eta 0:00:01\n", "   --------------------------- ---------- 184.3/251.4 kB 857.5 kB/s eta 0:00:01\n", "   ---------------------------------------- 251.4/251.4 kB 1.1 MB/s eta 0:00:00\n", "Downloading et_xmlfile-1.1.0-py3-none-any.whl (4.7 kB)\n", "Installing collected packages: et-xmlfile, openpyxl\n", "Successfully installed et-xmlfile-1.1.0 openpyxl-3.1.4\n"]}], "source": ["!pip install openpyxl"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["df_excel=pd.read_excel('data.xlsx')"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["df_excel.to_pickle('df_excel')"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Age</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Jack</td>\n", "      <td>34</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>John</td>\n", "      <td>31</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Name  Age\n", "0  Krish   32\n", "1   Jack   34\n", "2   <PERSON>   31"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.read_pickle('df_excel')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}