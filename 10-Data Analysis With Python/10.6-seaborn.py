#!/usr/bin/env python
# coding: utf-8

# #### Data Visualization With Seaborn
# Seaborn is a Python visualization library based on Matplotlib that provides a high-level interface for drawing attractive and informative statistical graphics. <PERSON><PERSON> helps in creating complex visualizations with just a few lines of code. In this lesson, we will cover the basics of <PERSON><PERSON>, including creating various types of plots and customizing them. 

# In[1]:


get_ipython().system('pip install seaborn')


# In[2]:


import seaborn as sns


# In[3]:


### Basic Plotting With Seaborn
tips=sns.load_dataset('tips')
tips


# In[5]:


##create a scatter plot
import matplotlib.pyplot as plt

sns.scatterplot(x='total_bill',y='tip',data=tips)
plt.title("Scatter Plot of Total Bill vs Tip")
plt.show()


# In[6]:


## Line Plot

sns.lineplot(x='size',y='total_bill',data=tips)
plt.title("Line Plot of Total bill by size")
plt.show()


# In[7]:


## Categorical Plots
## BAr Plot
sns.barplot(x='day',y='total_bill',data=tips)
plt.title('Bar Plot of Total Bill By Day')
plt.show()


# In[8]:


## Box Plot
sns.boxplot(x="day",y='total_bill',data=tips)


# In[9]:


## Violin Plot

sns.violinplot(x='day',y='total_bill',data=tips)


# In[12]:


### Histograms
sns.histplot(tips['total_bill'],bins=10,kde=True)


# In[14]:


## KDE Plot
sns.kdeplot(tips['total_bill'],fill=True)


# In[15]:


# Pairplot
sns.pairplot(tips)


# In[17]:


tips


# In[18]:


## HEatmap
corr=tips[['total_bill','tip','size']].corr()
corr


# In[19]:


sns.heatmap(corr,annot=True,cmap='coolwarm')


# In[20]:


import pandas as pd
sales_df=pd.read_csv('sales_data.csv')
sales_df.head()


# In[21]:


## Plot total sales by product
plt.figure(figsize=(10,6))
sns.barplot(x='Product Category',y="Total Revenue",data=sales_df,estimator=sum)
plt.title('Total Sales by Product')
plt.xlabel('Product')
plt.ylabel('Total Sales')
plt.show()


# In[23]:


## Plot total sales by Region
plt.figure(figsize=(10,6))
sns.barplot(x='Region',y="Total Revenue",data=sales_df,estimator=sum)
plt.title('Total Sales by Region')
plt.xlabel('Region')
plt.ylabel('Total Sales')
plt.show()


# In[ ]:




