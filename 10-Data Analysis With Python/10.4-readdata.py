#!/usr/bin/env python
# coding: utf-8

# ### Reading Data From Different Sources
# 

# In[4]:


import pandas as pd
from io import StringIO
Data = '{"employee_name": "<PERSON>", "email": "<EMAIL>", "job_profile": [{"title1":"Team Lead", "title2":"Sr. Developer"}]}'
df=pd.read_json(StringIO(Data))


# In[5]:


df


# In[6]:


df.to_json()


# In[7]:


df.to_json(orient='index')


# In[8]:


df.to_json(orient='records')


# In[9]:


df=pd.read_csv("https://archive.ics.uci.edu/ml/machine-learning-databases/wine/wine.data",header=None)


# In[10]:


df.head()


# In[11]:


df.to_csv("wine.csv")


# In[13]:


get_ipython().system('pip install lxml')


# In[17]:


get_ipython().system('pip install html5lib')
get_ipython().system('pip install beautifulsoup4')


# In[19]:


url="https://www.fdic.gov/resources/resolutions/bank-failures/failed-bank-list/"

df=pd.read_html(url)


# In[21]:


df[0]


# In[23]:


url="https://en.wikipedia.org/wiki/Mobile_country_code"
pd.read_html(url,match="Country",header=0)[0]


# In[25]:


get_ipython().system('pip install openpyxl')


# In[27]:


df_excel=pd.read_excel('data.xlsx')


# In[28]:


df_excel.to_pickle('df_excel')


# In[29]:


pd.read_pickle('df_excel')


# In[ ]:





# In[ ]:





# In[ ]:





# In[ ]:





# In[ ]:




