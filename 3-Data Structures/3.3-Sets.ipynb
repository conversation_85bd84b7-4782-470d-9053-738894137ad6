{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### Sets\n", "Sets are a built-in data type in Python used to store collections of unique items. They are unordered, meaning that the elements do not follow a specific order, and they do not allow duplicate elements. Sets are useful for membership tests, eliminating duplicate entries, and performing mathematical set operations like union, intersection, difference, and symmetric difference."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{1, 2, 3, 4, 5}\n", "<class 'set'>\n"]}], "source": ["##create a set\n", "my_set={1,2,3,4,5}\n", "print(my_set)\n", "print(type(my_set))"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'set'>\n"]}], "source": ["my_empty_set=set()\n", "print(type(my_empty_set))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{1, 2, 3, 4, 5, 6}\n"]}], "source": ["my_set=set([1,2,3,4,5,6])\n", "print(my_set)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{1, 2, 3, 4, 5, 6}\n"]}], "source": ["my_empty_set=set([1,2,3,6,5,4,5,6])\n", "print(my_empty_set)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{1, 2, 3, 4, 5, 6, 7}\n", "{1, 2, 3, 4, 5, 6, 7}\n"]}], "source": ["## Basics Sets Operation\n", "## Adiing and Removing Elements\n", "my_set.add(7)\n", "print(my_set)\n", "my_set.add(7)\n", "print(my_set)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{1, 2, 4, 5, 6, 7}\n"]}], "source": ["## Remove the elements from a set\n", "my_set.remove(3)\n", "print(my_set)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "10", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[11], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[43mmy_set\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m<PERSON>ove\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m10\u001b[39;49m\u001b[43m)\u001b[49m\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 10"]}], "source": ["my_set.remove(10)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{1, 2, 4, 5, 6, 7}\n"]}], "source": ["my_set.discard(11)\n", "print(my_set)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "{2, 4, 5, 6, 7}\n"]}], "source": ["## pop method\n", "removed_element=my_set.pop()\n", "print(removed_element)\n", "print(my_set)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["set()\n"]}], "source": ["## clear all the elements\n", "my_set.clear()\n", "print(my_set)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n", "False\n"]}], "source": ["## Set Memebership test\n", "my_set={1,2,3,4,5}\n", "print(3 in my_set)\n", "print(10 in my_set)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{1, 2, 3, 4, 5, 6, 7, 8, 9}\n", "{4, 5, 6}\n", "{4, 5, 6}\n"]}], "source": ["## MAthematical Operation\n", "set1={1,2,3,4,5,6}\n", "set2={4,5,6,7,8,9}\n", "\n", "### Union\n", "union_set=set1.union(set2)\n", "print(union_set)\n", "\n", "## Intersection\n", "intersection_set=set1.intersection(set2)\n", "print(intersection_set)\n", "\n", "set1.intersection_update(set2)\n", "print(set1)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{1, 2, 3}\n"]}], "source": ["set1={1,2,3,4,5,6}\n", "set2={4,5,6,7,8,9}\n", "\n", "## Difference \n", "print(set1.difference(set2))"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["{1, 2, 3, 4, 5, 6}"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["set1"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["{7, 8, 9}"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["set2.difference(set1)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["{1, 2, 3, 7, 8, 9}"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["## Symmetric Difference\n", "set1.symmetric_difference(set2)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False\n", "True\n"]}], "source": ["## Sets Methods\n", "set1={1,2,3,4,5}\n", "set2={3,4,5}\n", "\n", "## is subset\n", "print(set1.issubset(set2))\n", "\n", "print(set1.issuperset(set2))"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["{1, 2, 3, 4, 5}"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["lst=[1,2,2,3,4,4,5]\n", "\n", "set(lst)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'tutorial', 'we', 'discussing', 'this', 'In', 'about', 'sets', 'are'}\n", "8\n"]}], "source": ["### Counting Unique words in text\n", "\n", "text=\"In this tutorial we are discussing about sets\"\n", "words=text.split()\n", "\n", "## convert list of words to set to get unique words\n", "\n", "unique_words=set(words)\n", "print(unique_words)\n", "print(len(unique_words))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Conclusion\n", "Sets are a powerful and flexible data type in Python that provide a way to store collections of unique elements. They support various operations such as union, intersection, difference, and symmetric difference, which are useful for mathematical computations. Understanding how to use sets and their associated methods can help you write more efficient and clean Python code, especially when dealing with unique collections and membership tests."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}