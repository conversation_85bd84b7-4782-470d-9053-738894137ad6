{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### Introduction To Lists\n", "- Lists are ordered, mutable collections of items.\n", "- They can contain items of different data types."]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Video Outline:\n", "1. Introduction to Lists\n", "2. Creating Lists\n", "3. Accessing List Elements\n", "4. Modifying List Elements\n", "5. List Methods\n", "6. Slicing Lists\n", "7. Iterating Over Lists\n", "8. List Comprehensions\n", "9. Nested Lists\n", "10. Practical Examples and Common Errors"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'list'>\n"]}], "source": ["lst=[]\n", "print(type(lst))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['<PERSON><PERSON>', '<PERSON>', '<PERSON>', 1, 2, 3, 4, 5]\n"]}], "source": ["names=[\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>\",1,2,3,4,5]\n", "print(names)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1, 'Hello', 3.14, <PERSON>]\n"]}], "source": ["mixed_list=[1,\"Hello\",3.14,True]\n", "print(mixed_list)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["### Accessing List Elements\n", "\n", "fruits=[\"apple\",\"banana\",\"cherry\",\"kiwi\",\"gauva\"]"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["apple\n", "cherry\n", "gauva\n", "gauva\n"]}], "source": ["print(fruits[0])\n", "print(fruits[2])\n", "print(fruits[4])\n", "print(fruits[-1])"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['banana', 'cherry', 'kiwi', 'gauva']\n", "['banana', 'cherry']\n"]}], "source": ["print(fruits[1:])\n", "print(fruits[1:3])"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["['apple', 'banana', 'cherry', 'kiwi', 'gauva']"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["## Modifying The List elements\n", "fruits"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['apple', 'watermelon', 'cherry', 'kiwi', 'gauva']\n"]}], "source": ["fruits[1]=\"watermelon\"\n", "print(fruits)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["fruits[1:]=\"watermelon\""]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["['apple', 'w', 'a', 't', 'e', 'r', 'm', 'e', 'l', 'o', 'n']"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["fruits"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["fruits=[\"apple\",\"banana\",\"cherry\",\"kiwi\",\"gauva\"]"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['apple', 'banana', 'cherry', 'kiwi', 'gauva', 'orange']\n"]}], "source": ["## List Methods\n", "\n", "fruits.append(\"orange\") ## Add an item to the end\n", "print(fruits)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['apple', 'watermelon', 'banana', 'banana', 'cherry', 'kiwi', 'gauva', 'orange']\n"]}], "source": ["fruits.insert(1,\"watermelon\")\n", "print(fruits)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['apple', 'watermelon', 'banana', 'cherry', 'kiwi', 'gauva', 'orange']\n"]}], "source": ["fruits.remove(\"banana\") ## Removing the first occurance of an item\n", "print(fruits)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["orange\n", "['apple', 'watermelon', 'banana', 'cherry', 'kiwi', 'gauva']\n"]}], "source": ["## Remove and return the last element\n", "popped_fruits=fruits.pop()\n", "print(popped_fruits)\n", "print(fruits)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3\n"]}], "source": ["index=fruits.index(\"cherry\")\n", "print(index)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2\n"]}], "source": ["fruits.insert(2,\"banana\")\n", "print(fruits.count(\"banana\"))"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["['apple', 'watermelon', 'banana', 'banana', 'cherry', 'kiwi', 'gauva']"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["fruits"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["fruits.sort() ## SSorts the list in ascending order"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["['apple', 'banana', 'banana', 'cherry', 'gauva', 'kiwi', 'watermelon']"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["fruits"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["fruits.reverse() ## REverse the list"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/plain": ["['watermelon', 'kiwi', 'gauva', 'cherry', 'banana', 'banana', 'apple']"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["fruits"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[]\n"]}], "source": ["fruits.clear() ## Remove all items from the list\n", "\n", "print(fruits)"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[3, 4, 5]\n", "[1, 2, 3, 4, 5]\n", "[6, 7, 8, 9, 10]\n", "[1, 3, 5, 7, 9]\n", "[10, 9, 8, 7, 6, 5, 4, 3, 2, 1]\n"]}], "source": ["## Slicing List\n", "numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]\n", "print(numbers[2:5])\n", "print(numbers[:5])\n", "print(numbers[5:])\n", "print(numbers[::2])\n", "print(numbers[::-1])"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 4, 7, 10]"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["numbers[::3]"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"text/plain": ["[10, 8, 6, 4, 2]"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["numbers[::-2]"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "2\n", "3\n", "4\n", "5\n", "6\n", "7\n", "8\n", "9\n", "10\n"]}], "source": ["### Iterating Over List\n", "\n", "for number in numbers:\n", "    print(number)"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0 1\n", "1 2\n", "2 3\n", "3 4\n", "4 5\n", "5 6\n", "6 7\n", "7 8\n", "8 9\n", "9 10\n"]}], "source": ["## Iterating with index\n", "for index,number in enumerate(numbers):\n", "    print(index,number)"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0, 1, 4, 9, 16, 25, 36, 49, 64, 81]\n"]}], "source": ["## List comprehension\n", "lst=[]\n", "for x in range(10):\n", "    lst.append(x**2)\n", "\n", "print(lst)\n"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"data": {"text/plain": ["[0, 1, 4, 9, 16, 25, 36, 49, 64, 81]"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["[x**2 for x in range(10)]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### List Comprehension\n", "\n", "Basics Syantax            [expression for item in iterable]\n", "\n", "with conditional logic    [expression for item in iterable if condition]\n", "\n", "Nested List Comprehension [expression for item1 in iterable1 for item2 in iterable2]\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0, 1, 4, 9, 16, 25, 36, 49, 64, 81]\n"]}], "source": ["### Basic List Comphrension\n", "\n", "sqaure=[num**2 for num in range(10)]\n", "print(sqaure)"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0, 2, 4, 6, 8]\n"]}], "source": ["### List Comprehension with Condition\n", "lst=[]\n", "for i in range(10):\n", "    if i%2==0:\n", "        lst.append(i)\n", "\n", "print(lst)\n", "        "]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0, 2, 4, 6, 8]\n"]}], "source": ["even_numbers=[num for num in range(10) if num%2==0]\n", "print(even_numbers)"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[1, 'a'], [1, 'b'], [1, 'c'], [1, 'd'], [2, 'a'], [2, 'b'], [2, 'c'], [2, 'd'], [3, 'a'], [3, 'b'], [3, 'c'], [3, 'd'], [4, 'a'], [4, 'b'], [4, 'c'], [4, 'd']]\n"]}], "source": ["## Nested List Comphrension\n", "\n", "lst1=[1,2,3,4]\n", "lst2=['a','b','c','d']\n", "\n", "pair=[[i,j] for i in lst1 for j in lst2]\n", "\n", "print(pair)\n"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[5, 5, 6, 4, 13]\n"]}], "source": ["## List Comprehension with function calls\n", "words = [\"hello\", \"world\", \"python\", \"list\", \"comprehension\"]\n", "lengths = [len(word) for word in words]\n", "print(lengths)  # Output: [5, 5, 6, 4, 13]\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Conclusion\n", "List comprehensions are a powerful and concise way to create lists in Python. They are syntactically compact and can replace more verbose looping constructs. Understanding the syntax of list comprehensions will help you write cleaner and more efficient Python code."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}