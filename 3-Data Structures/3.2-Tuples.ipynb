{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### Tu<PERSON>\n", "Video Outline:\n", "1. Introduction to <PERSON><PERSON>\n", "2. <PERSON><PERSON><PERSON>\n", "3. Accessing Tuple Elements\n", "4. <PERSON><PERSON>\n", "5. Immutable Nature of Tuples\n", "6. <PERSON><PERSON>s\n", "7. Packing and Unpacking Tuples\n", "8. <PERSON><PERSON>\n", "9. Practical Examples and Common Errors\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "##### Introduction to <PERSON><PERSON>\n", "Explanation:\n", "\n", "Tuples are ordered collections of items that are immutable.\n", "They are similar to lists, but their immutability makes them different.\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["()\n", "<class 'tuple'>\n"]}], "source": ["## creating a tuple\n", "empty_tuple=()\n", "print(empty_tuple)\n", "print(type(empty_tuple))"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'list'>\n", "<class 'tuple'>\n"]}], "source": ["lst=list()\n", "print(type(lst))\n", "tpl=tuple()\n", "print(type(tpl))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1, 2, 3, 4, 5, 6)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["numbers=tuple([1,2,3,4,5,6])\n", "numbers"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 3, 4, 5, 6]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["list((1,2,3,4,5,6))"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1, 'Hello World', 3.14, <PERSON>)\n"]}], "source": ["mixed_tuple=(1,\"Hello World\",3.14, True)\n", "print(mixed_tuple)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1, 2, 3, 4, 5, 6)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["## Accessing Tuple Elements\n", "\n", "numbers"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3\n", "6\n"]}], "source": ["print(numbers[2])\n", "print(numbers[-1])"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1, 2, 3, 4)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["numbers[0:4]"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["(6, 5, 4, 3, 2, 1)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["numbers[::-1]"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1, 2, 3, 4, 5, 6, 1, 'Hello World', 3.14, True)\n"]}], "source": ["## Tuple Operations\n", "\n", "concatenation_tuple=numbers + mixed_tuple\n", "print(concatenation_tuple)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1,\n", " 'Hello World',\n", " 3.14,\n", " True,\n", " 1,\n", " 'Hello World',\n", " 3.14,\n", " True,\n", " 1,\n", " 'Hello World',\n", " 3.14,\n", " True)"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["mixed_tuple * 3"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1, 2, 3, 4, 5, 6, 1, 2, 3, 4, 5, 6, 1, 2, 3, 4, 5, 6)"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["numbers *3"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1, 2, 3, 4, 5]\n", "[1, '<PERSON><PERSON>', 3, 4, 5]\n"]}], "source": ["## Immutable Nature Of Tuples\n", "## Tuples are immutable, meaning their elements cannot be changed once assigned.\n", "\n", "lst=[1,2,3,4,5]\n", "print(lst)\n", "\n", "lst[1]=\"Krish\"\n", "print(lst)\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "'tuple' object does not support item assignment", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mTypeError\u001b[0m                                 Trace<PERSON> (most recent call last)", "Cell \u001b[1;32mIn[23], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[43mnumbers\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m]\u001b[49m\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m<PERSON>ris<PERSON>\u001b[39m\u001b[38;5;124m\"\u001b[39m\n", "\u001b[1;31mTypeError\u001b[0m: 'tuple' object does not support item assignment"]}], "source": ["numbers[1]=\"Krish\""]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1, 2, 3, 4, 5, 6)"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["numbers"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "2\n"]}], "source": ["## Tuple Methods\n", "print(numbers.count(1))\n", "print(numbers.index(3))"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1, 'Hello', 3.14)\n"]}], "source": ["## Packing and Unpacking tuple\n", "## packing\n", "packed_tuple=1,\"Hello\",3.14\n", "print(packed_tuple)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "Hello\n", "3.14\n"]}], "source": ["##unpacking a tuple\n", "a,b,c=packed_tuple\n", "\n", "print(a)\n", "print(b)\n", "print(c)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "[2, 3, 4, 5]\n", "6\n"]}], "source": ["## Unpacking with *\n", "numbers=(1,2,3,4,5,6)\n", "first,*middle,last=numbers\n", "print(first)\n", "print(middle)\n", "print(last)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 3]"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["## N<PERSON> Tuple\n", "## Nested List\n", "lst=[[1,2,3,4],[6,7,8,9],[1,\"Hello\",3.14,\"c\"]]\n", "lst[0][0:3]"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1, 'Hello', 3.14)"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["lst=[[1,2,3,4],[6,7,8,9],(1,\"Hello\",3.14,\"c\")]\n", "lst[2][0:3]"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1, 2, 3)\n", "c\n"]}], "source": ["nested_tuple = ((1, 2, 3), (\"a\", \"b\", \"c\"), (True, False))\n", "\n", "## access the elements inside a tuple\n", "print(nested_tuple[0])\n", "print(nested_tuple[1][2])"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 2 3 \n", "a b c \n", "True False \n"]}], "source": ["## iterating over nested tuples\n", "for sub_tuple in nested_tuple:\n", "    for item in sub_tuple:\n", "        print(item,end=\" \")\n", "    print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Conclusion\n", "Tuples are versatile and useful in many real-world scenarios where an immutable and ordered collection of items is required. They are commonly used in data structures, function arguments and return values, and as dictionary keys. Understanding how to leverage tuples effectively can improve the efficiency and readability of your Python code."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}