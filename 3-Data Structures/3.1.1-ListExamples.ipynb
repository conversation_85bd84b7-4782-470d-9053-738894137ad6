{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### Real-World Examples Using Lists in Python\n", "Lists are one of the most commonly used data structures in Python, thanks to their versatility and ease of use. Here are several practical examples that illustrate their use in real-world scenarios"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Example 1. Manage A To Do List\n", "- Create a To Do List To Keep Track OF Tasks"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Don't forgrt to pay the utility bills\n", "To Do List remaining\n", "-Buy Groceries\n", "-Pay bills\n", "-Schedule meeting\n", "-Go For a Run\n"]}], "source": ["to_do_list=[\"Buy Groceries\",\"Clean the house\",\"Pay bills\"]\n", "\n", "## Adding to task\n", "to_do_list.append(\"Schedule meeting\")\n", "to_do_list.append(\"Go For a Run\")\n", "\n", "## Removing a completed task\n", "to_do_list.remove(\"Clean the house\")\n", "\n", "## checking if a task is in the list\n", "if \"Pay bills\" in to_do_list:\n", "    print(\"Don't forgrt to pay the utility bills\")\n", "\n", "print(\"To Do List remaining\")\n", "for task in to_do_list:\n", "    print(f\"-{task}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Example 2: Organizing Student Grades\n", "- Create a list to store and calculate average grades for students"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Average Grade: 88.00\n", "Highest Grade: 95\n", "Lowest Grade: 78\n"]}], "source": ["# Organizing student grades\n", "grades = [85, 92, 78, 90, 88]\n", "\n", "# Adding a new grade\n", "grades.append(95)\n", "\n", "# Calculating the average grade\n", "average_grade = sum(grades) / len(grades)\n", "print(f\"Average Grade: {average_grade:.2f}\")\n", "\n", "# Finding the highest and lowest grades\n", "highest_grade = max(grades)\n", "lowest_grade = min(grades)\n", "print(f\"Highest Grade: {highest_grade}\")\n", "print(f\"Lowest Grade: {lowest_grade}\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Example 3: Managing An Inventory\n", "- Use a list to manage inventory items in a store"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["oranges are in stock.\n", "Inventory List:\n", "- apples\n", "- oranges\n", "- grapes\n", "- strawberries\n"]}], "source": ["# Managing an inventory\n", "inventory = [\"apples\", \"bananas\", \"oranges\", \"grapes\"]\n", "\n", "# Adding a new item\n", "inventory.append(\"strawberries\")\n", "\n", "# Removing an item that is out of stock\n", "inventory.remove(\"bananas\")\n", "\n", "# Checking if an item is in stock\n", "item = \"oranges\"\n", "if item in inventory:\n", "    print(f\"{item} are in stock.\")\n", "else:\n", "    print(f\"{item} are out of stock.\")\n", "\n", "# Printing the inventory\n", "print(\"Inventory List:\")\n", "for item in inventory:\n", "    print(f\"- {item}\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Example 4:Collecting User Feedback\n", "- Use a list to collect and analyze user feedback."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Positive Feedback Count: 2\n", "User Feedback:\n", "- Great service!\n", "- Very satisfied\n", "- Could be better\n", "- Excellent experience\n", "- Not happy with the service\n"]}], "source": ["# Collecting user feedback\n", "feedback = [\"Great service!\", \"Very satisfied\", \"Could be better\", \"Excellent experience\"]\n", "\n", "# Adding new feedback\n", "feedback.append(\"Not happy with the service\")\n", "\n", "# Counting specific feedback\n", "positive_feedback_count = sum(1 for comment in feedback if \"great\" in comment.lower() or \"excellent\" in comment.lower())\n", "print(f\"Positive Feedback Count: {positive_feedback_count}\")\n", "\n", "# Printing all feedback\n", "print(\"User Feedback:\")\n", "for comment in feedback:\n", "    print(f\"- {comment}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}