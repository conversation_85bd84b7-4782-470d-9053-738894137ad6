{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### SQL And SQLite\n", "SQL (Structured Query Language) is a standard language for managing and manipulating relational databases. SQLite is a self-contained, serverless, and zero-configuration database engine that is widely used for embedded database systems. In this lesson, we will cover the basics of SQL and SQLite, including creating databases, tables, and performing various SQL operations."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sqlite3"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["<sqlite3.Connection at 0x20061df8d60>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["## Connect to an SQLite database\n", "connection=sqlite3.connect('example.db')\n", "connection"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["cursor=connection.cursor()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["## Create a Table\n", "cursor.execute('''\n", "Create Table If Not Exists employees(\n", "    id Integer Primary Key,\n", "    name Text Not Null,\n", "    age Integer,\n", "    department text\n", "    )\n", "''')\n", "\n", "## Commit the changes\n", "connection.commit()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["<sqlite3.Cursor at 0x200619658c0>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["cursor.execute('''\n", "Select * from employees\n", "    \n", "''')"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["## Insert the data in sqlite table\n", "cursor.execute('''\n", "Insert Into employees(name,age,department)\n", "               values('<PERSON><PERSON>',32,'Data Scientist')\n", "\n", "''')\n", "\n", "cursor.execute('''\n", "INSERT INTO employees (name, age, department)\n", "VALUES ('<PERSON>', 25, 'Engineering')\n", "''')\n", "\n", "cursor.execute('''\n", "INSERT INTO employees (name, age, department)\n", "VALUES ('Charlie', 35, 'Finance')\n", "''')\n", "\n", "## commi the changes\n", "connection.commit()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1, '<PERSON><PERSON>', 32, '<PERSON> Scientist')\n", "(2, '<PERSON>', 25, '<PERSON>')\n", "(3, '<PERSON>', 35, '<PERSON>')\n"]}], "source": ["## Query the data from the table\n", "cursor.execute('Select * from employees')\n", "rows=cursor.fetchall()\n", "\n", "## print the queried data\n", "\n", "for row in rows:\n", "    print(row)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["## Update the data in the table\n", "cursor.execute('''\n", "UPDATE employees\n", "Set age=34\n", "where name=\"<PERSON><PERSON>\"\n", "''')\n", "\n", "connection.commit()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1, '<PERSON><PERSON>', 34, '<PERSON> Scientist')\n", "(2, '<PERSON>', 25, '<PERSON>')\n", "(3, '<PERSON>', 35, '<PERSON>')\n"]}], "source": ["## Query the data from the table\n", "cursor.execute('Select * from employees')\n", "rows=cursor.fetchall()\n", "\n", "## print the queried data\n", "\n", "for row in rows:\n", "    print(row)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["## Delete the data from the table\n", "cursor.execute('''\n", "Delete from employees\n", "               where name ='<PERSON>'\n", "''')\n", "\n", "connection.commit()"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1, '<PERSON><PERSON>', 34, '<PERSON> Scientist')\n", "(3, '<PERSON>', 35, '<PERSON>')\n"]}], "source": ["## Query the data from the table\n", "cursor.execute('Select * from employees')\n", "rows=cursor.fetchall()\n", "\n", "## print the queried data\n", "\n", "for row in rows:\n", "    print(row)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["## Working Wwith Sales Data\n", "# Connect to an SQLite database\n", "connection = sqlite3.connect('sales_data.db')\n", "cursor = connection.cursor()\n", "\n", "# Create a table for sales data\n", "cursor.execute('''\n", "CREATE TABLE IF NOT EXISTS sales (\n", "    id INTEGER PRIMARY KEY,\n", "    date TEXT NOT NULL,\n", "    product TEXT NOT NULL,\n", "    sales INTEGER,\n", "    region TEXT\n", ")\n", "''')\n", "\n", "# Insert data into the sales table\n", "sales_data = [\n", "    ('2023-01-01', 'Product1', 100, 'North'),\n", "    ('2023-01-02', 'Product2', 200, 'South'),\n", "    ('2023-01-03', 'Product1', 150, 'East'),\n", "    ('2023-01-04', 'Product3', 250, 'West'),\n", "    ('2023-01-05', 'Product2', 300, 'North')\n", "]\n", "\n", "cursor.executemany('''\n", "Insert into sales(date,product,sales,region)\n", "                   values(?,?,?,?)\n", "''',sales_data)\n", "\n", "connection.commit()"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1, '2023-01-01', 'Product1', 100, 'North')\n", "(2, '2023-01-02', 'Product2', 200, 'South')\n", "(3, '2023-01-03', 'Product1', 150, 'East')\n", "(4, '2023-01-04', 'Product3', 250, 'West')\n", "(5, '2023-01-05', 'Product2', 300, 'North')\n"]}], "source": ["# Query data from the sales table\n", "cursor.execute('SELECT * FROM sales')\n", "rows = cursor.fetchall()\n", "\n", "# Print the queried data\n", "for row in rows:\n", "    print(row)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["## close the connection\n", "connection.close()"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"ename": "ProgrammingError", "evalue": "Cannot operate on a closed database.", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mProgrammingError\u001b[0m                          <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[22], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# Query data from the sales table\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m \u001b[43mcursor\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mSELECT * FROM sales\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m      3\u001b[0m rows \u001b[38;5;241m=\u001b[39m cursor\u001b[38;5;241m.\u001b[39mfetchall()\n\u001b[0;32m      5\u001b[0m \u001b[38;5;66;03m# Print the queried data\u001b[39;00m\n", "\u001b[1;31mProgrammingError\u001b[0m: Cannot operate on a closed database."]}], "source": ["# Query data from the sales table\n", "cursor.execute('SELECT * FROM sales')\n", "rows = cursor.fetchall()\n", "\n", "# Print the queried data\n", "for row in rows:\n", "    print(row)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}