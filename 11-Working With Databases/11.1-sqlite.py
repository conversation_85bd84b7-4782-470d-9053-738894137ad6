#!/usr/bin/env python
# coding: utf-8

# #### SQL And SQLite
# SQL (Structured Query Language) is a standard language for managing and manipulating relational databases. SQLite is a self-contained, serverless, and zero-configuration database engine that is widely used for embedded database systems. In this lesson, we will cover the basics of SQL and SQLite, including creating databases, tables, and performing various SQL operations.

# In[1]:


import sqlite3


# In[3]:


## Connect to an SQLite database
connection=sqlite3.connect('example.db')
connection


# In[5]:


cursor=connection.cursor()


# In[6]:


## Create a Table
cursor.execute('''
Create Table If Not Exists employees(
    id Integer Primary Key,
    name Text Not Null,
    age Integer,
    department text
    )
''')

## Commit the changes
connection.commit()


# In[8]:


cursor.execute('''
Select * from employees

''')


# In[9]:


## Insert the data in sqlite table
cursor.execute('''
Insert Into employees(name,age,department)
               values('<PERSON><PERSON>',32,'Data Scientist')

''')

cursor.execute('''
INSERT INTO employees (name, age, department)
VALUES ('Bob', 25, 'Engineering')
''')

cursor.execute('''
INSERT INTO employees (name, age, department)
VALUES ('Charlie', 35, 'Finance')
''')

## commi the changes
connection.commit()


# In[10]:


## Query the data from the table
cursor.execute('Select * from employees')
rows=cursor.fetchall()

## print the queried data

for row in rows:
    print(row)


# In[12]:


## Update the data in the table
cursor.execute('''
UPDATE employees
Set age=34
where name="Krish"
''')

connection.commit()


# In[13]:


## Query the data from the table
cursor.execute('Select * from employees')
rows=cursor.fetchall()

## print the queried data

for row in rows:
    print(row)


# In[16]:


## Delete the data from the table
cursor.execute('''
Delete from employees
               where name ='Bob'
''')

connection.commit()


# In[17]:


## Query the data from the table
cursor.execute('Select * from employees')
rows=cursor.fetchall()

## print the queried data

for row in rows:
    print(row)


# In[19]:


## Working Wwith Sales Data
# Connect to an SQLite database
connection = sqlite3.connect('sales_data.db')
cursor = connection.cursor()

# Create a table for sales data
cursor.execute('''
CREATE TABLE IF NOT EXISTS sales (
    id INTEGER PRIMARY KEY,
    date TEXT NOT NULL,
    product TEXT NOT NULL,
    sales INTEGER,
    region TEXT
)
''')

# Insert data into the sales table
sales_data = [
    ('2023-01-01', 'Product1', 100, 'North'),
    ('2023-01-02', 'Product2', 200, 'South'),
    ('2023-01-03', 'Product1', 150, 'East'),
    ('2023-01-04', 'Product3', 250, 'West'),
    ('2023-01-05', 'Product2', 300, 'North')
]

cursor.executemany('''
Insert into sales(date,product,sales,region)
                   values(?,?,?,?)
''',sales_data)

connection.commit()


# In[20]:


# Query data from the sales table
cursor.execute('SELECT * FROM sales')
rows = cursor.fetchall()

# Print the queried data
for row in rows:
    print(row)


# In[21]:


## close the connection
connection.close()


# In[22]:


# Query data from the sales table
cursor.execute('SELECT * FROM sales')
rows = cursor.fetchall()

# Print the queried data
for row in rows:
    print(row)


# In[ ]:




