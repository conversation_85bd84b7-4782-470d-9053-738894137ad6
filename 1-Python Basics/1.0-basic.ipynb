{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Syntax and Semantics in Python\n", "Video Outline:\n", "- Single line Comments and multiline comments \n", "- Definition of Syntax and Semantics\n", "- Basic Syntax Rules in Python\n", "- Understanding Semantics in Python\n", "- Common Syntax Errors and How to Avoid Them\n", "- Practical Code Examples"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Syntax refers to the set of rules that defines the combinations of symbols that are considered to be correctly structured programs in a language. In simpler terms, syntax is about the correct arrangement of words and symbols in a code.\n", "\n", "Semantics refers to the meaning or the interpretation of the symbols, characters, and commands in a language. It is about what the code is supposed to do when it runs."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON>\n", "<PERSON><PERSON>\n"]}], "source": ["## Basic Syntax Rules In Python\n", "## Case sensitivity- Python is case sensitive\n", "\n", "name=\"<PERSON><PERSON>\"\n", "Name=\"Na<PERSON>\"\n", "\n", "print(name)\n", "print(Name)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Indentation\n", "Indentation in Python is used to define the structure and hierarchy of the code. Unlike many other programming languages that use braces {} to delimit blocks of code, Python uses indentation to determine the grouping of statements. This means that all the statements within a block must be indented at the same level."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["32\n", "32\n"]}], "source": ["## Indentation\n", "## Python uses indentation to define blocks of code. Consistent use of spaces (commonly 4) or a tab is required.\n", "\n", "age=32\n", "if age>30:\n", "    \n", "    print(age)\n", "    \n", "print(age)\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello World\n"]}], "source": ["## This is a single line comment\n", "print(\"Hello World\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["43\n"]}], "source": ["## Line Continuation\n", "##Use a backslash (\\) to continue a statement to the next line\n", "\n", "total=1+2+3+4+5+6+7+\\\n", "4+5+6\n", "\n", "print(total)\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["15\n"]}], "source": ["## Multiple Statements on a single line\n", "x=5;y=10;z=x+y\n", "print(z)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["##Understand  Semnatics In Python\n", "# variable assignment\n", "age=32 ##age is an integer\n", "name=\"<PERSON><PERSON>\" ##name is a string"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["int"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "\n", "type(age)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["str"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["type(name)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'int'>\n", "<class 'str'>\n"]}], "source": ["## Type Inference\n", "variable=10\n", "print(type(variable))\n", "variable=\"Krish\"\n", "print(type(variable))"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["32\n"]}], "source": ["age=32\n", "if age>30:\n", "    print(age)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'b' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[15], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m a\u001b[38;5;241m=\u001b[39m\u001b[43mb\u001b[49m\n", "\u001b[1;31mNameError\u001b[0m: name 'b' is not defined"]}], "source": ["## Name Error\n", "a=b"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Correct Indentation\n", "This will print\n", "Outside the if block\n"]}], "source": ["## Code exmaples of indentation\n", "if True:\n", "    print(\"Correct Indentation\")\n", "    if False:\n", "        print(\"This ont print\")\n", "    print(\"This will print\")\n", "print(\"Outside the if block\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Conclusion:\n", "Understanding the syntax and semantics of Python is crucial for writing correct and meaningful programs. Syntax ensures the code is properly structured, while semantics ensures the code behaves as expected. Mastering these concepts will help in writing efficient and error-free Python code."]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}