{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Deep Dive into Operators"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Video Outline:\n", "1. Introduction to Operators\n", "2. Arithmetic Operators\n", "   - Addition\n", "   - Subtraction\n", "   - Multiplication\n", "   - Division\n", "   - Floor Division\n", "   - <PERSON><PERSON><PERSON>\n", "   - Exponentiation\n", "3. Comparison Operators\n", "   - Equal to\n", "   - Not equal to\n", "   - Greater than\n", "   - Less than\n", "   - Greater than or equal to \n", "   - Less than or equal to\n", "4. Logical Operators\n", "   - AND\n", "   - OR\n", "   - NOT\n", "5. Practical Examples and Common Errors"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["15\n", "5\n", "50\n", "2.0\n", "2\n", "0\n", "100000\n"]}], "source": ["## Arithmethic Operation\n", "\n", "a=10\n", "b = 5\n", "\n", "add_result=a+b  #addiiton\n", "sub_result=a-b  #substraction \n", "mult_result=a*b #multiplication\n", "div_result=a/b  #division\n", "floor_div_result=a//b ## floor division\n", "modulus_result=a%b #modulus operation\n", "\n", "exponent_result=a**b ## Exponentiation\n", "\n", "\n", "print(add_result)\n", "print(sub_result)\n", "print(mult_result)\n", "print(div_result)\n", "print(floor_div_result)\n", "print(modulus_result)\n", "print(exponent_result)\n", "\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["2.0"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["10/5"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["4.2"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["21/5"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["4"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["21//5"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Comparison Operators"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["## Comparison Operators\n", "## == Equal to\n", "a=10\n", "b=10\n", "\n", "a==b"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["str1=\"Krish\"\n", "str2=\"Krish\"\n", "\n", "str1==str2"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["## Not Equal to !=\n", "str1!=str2"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["str3=\"Krish\"\n", "str4=\"krish\"\n", "\n", "str3!=str4"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# greater than >\n", "\n", "num1=45\n", "num2=55\n", "\n", "num1>num2"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n"]}], "source": ["## less than <\n", "\n", "print(num1<num2)\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n"]}], "source": ["#greater than or equal to\n", "number1=45\n", "number2=45\n", "\n", "print(number1>=number2)\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n"]}], "source": ["#less than or equal to\n", "number1=44\n", "number2=45\n", "\n", "print(number1<=number2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Logical Operators"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n"]}], "source": ["## And ,Not,OR\n", "X=True\n", "Y=True\n", "\n", "result =X and Y\n", "print(result)\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False\n"]}], "source": ["X=False\n", "Y=True\n", "\n", "result =X and Y\n", "print(result)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False\n"]}], "source": ["## OR\n", "X=False\n", "Y=False\n", "\n", "result =X or Y\n", "print(result)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["# Not operator\n", "X=False\n", "not X"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Addition: 16.0\n", "Subtraction: 8.0\n", "Multiplication: 48.0\n", "Division: 3.0\n", "Floor Division: 3.0\n", "Modulus: 0.0\n", "Exponentiation: 20736.0\n"]}], "source": ["## Simple Calculator\n", "\n", "# Simple calculator\n", "num1 = float(input(\"Enter first number: \"))\n", "num2 = float(input(\"Enter second number: \"))\n", "\n", "# Performing arithmetic operations\n", "addition = num1 + num2\n", "subtraction = num1 - num2\n", "multiplication = num1 * num2\n", "division = num1 / num2\n", "floor_division = num1 // num2\n", "modulus = num1 % num2\n", "exponentiation = num1 ** num2\n", "\n", "# Displaying results\n", "print(\"Addition:\", addition)\n", "print(\"Subtraction:\", subtraction)\n", "print(\"Multiplication:\", multiplication)\n", "print(\"Division:\", division)\n", "print(\"Floor Division:\", floor_division)\n", "print(\"Modulus:\", modulus)\n", "print(\"Exponentiation:\", exponentiation)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}