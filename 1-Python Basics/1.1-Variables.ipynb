{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Variables\n", "Variables are fundamental elements in programming used to store data that can be referenced and manipulated in a program. In Python, variables are created when you assign a value to them, and they do not need explicit declaration to reserve memory space. The declaration happens automatically when you assign a value to a variable.\n", "\n", "Video Outline:\n", "- Introduction to Variables\n", "- Declaring and Assigning Variables\n", "- Naming Conventions\n", "- Understanding Variable Types\n", "- Type Checking and Conversion\n", "- Dynamic Typing\n", "- Practical Examples and Common Errors"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["a=100"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["age : 32\n", "Height: 6.1\n", "Name: <PERSON><PERSON>\n"]}], "source": ["## Declaring And Assigning Variables\n", "\n", "age=32\n", "height=6.1\n", "name=\"<PERSON><PERSON>\"\n", "is_student=True\n", "\n", "## printing the variables\n", "\n", "print(\"age :\",age)\n", "print(\"Height:\",height)\n", "print(\"Name:\",name)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["## Naming Conventions\n", "## Variable names should be descriptive\n", "## They must start with a letter or an '_' and contains letter,numbers and underscores\n", "## variables names case sensitive\n", "\n", "#valid variable names\n", "\n", "first_name=\"<PERSON><PERSON><PERSON>\"\n", "last_name=\"<PERSON><PERSON>\""]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid syntax. Maybe you meant '==' or ':=' instead of '='? (1710987940.py, line 4)", "output_type": "error", "traceback": ["\u001b[1;36m  Cell \u001b[1;32mIn[6], line 4\u001b[1;36m\u001b[0m\n\u001b[1;33m    @name=\"<PERSON><PERSON>\"\u001b[0m\n\u001b[1;37m     ^\u001b[0m\n\u001b[1;31mSyntaxError\u001b[0m\u001b[1;31m:\u001b[0m invalid syntax. Maybe you meant '==' or ':=' instead of '='?\n"]}], "source": ["# Invalid variable names\n", "#2age=30\n", "#first-name=\"<PERSON><PERSON>\"\n", "##@name=\"<PERSON><PERSON>\""]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["## case sensitivity\n", "name=\"<PERSON><PERSON>\"\n", "Name=\"Na<PERSON>\"\n", "\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'str'>\n"]}], "source": ["## Understnading Variable types\n", "## Python is dynamically typed,type of a variable is determined at runtime\n", "age=25 #int\n", "height=6.1 #float\n", "name=\"KRish\" #str\n", "is_student=True #bool\n", "\n", "print(type(name))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["float"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["## Type Checking and Conversion\n", "\n", "type(height)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'int'>\n", "25\n", "<class 'str'>\n"]}], "source": ["age=25\n", "print(type(age))\n", "\n", "# Type conversion\n", "age_str=str(age)\n", "print(age_str)\n", "print(type(age_str))"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'int'>\n"]}], "source": ["age='25'\n", "print(type(int(age)))"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "invalid literal for int() with base 10: '<PERSON><PERSON>'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[16], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m name\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m<PERSON>ris<PERSON>\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m----> 2\u001b[0m \u001b[38;5;28;43mint\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mname\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[1;31mValueError\u001b[0m: invalid literal for int() with base 10: '<PERSON>h'"]}], "source": ["name=\"<PERSON><PERSON>\"\n", "int(name)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["float"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["height=5.11\n", "type(height)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["5.0"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["float(int(height))"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["10 <class 'int'>\n", "Hello <class 'str'>\n", "3.14 <class 'float'>\n"]}], "source": ["## Dynamic Typing\n", "## Python allows the type of a vraible to change as the program executes\n", "var=10 #int\n", "print(var,type(var))\n", "\n", "var=\"Hello\"\n", "print(var,type(var))\n", "\n", "var=3.14\n", "print(var,type(var))\n", "\n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["23 <class 'int'>\n"]}], "source": ["## input\n", "\n", "age=int(input(\"What is the age\"))\n", "print(age,type(age))"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sum: 66.0\n", "Difference: 46.0\n", "Product: 560.0\n", "Quotient: 5.6\n"]}], "source": ["### Simple calculator\n", "num1 = float(input(\"Enter first number: \"))\n", "num2 = float(input(\"Enter second number: \"))\n", "\n", "sum = num1 + num2\n", "difference = num1 - num2\n", "product = num1 * num2\n", "quotient = num1 / num2\n", "\n", "print(\"Sum:\", sum)\n", "print(\"Difference:\", difference)\n", "print(\"Product:\", product)\n", "print(\"Quotient:\", quotient)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["23 <class 'int'>\n"]}], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### Conclusion:\n", "Variables are essential in Python programming for storing and manipulating data. Understanding how to declare, assign, and use variables effectively is crucial for writing functional and efficient code. Following proper naming conventions and understanding variable types will help in maintaining readability and consistency in your code."]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}