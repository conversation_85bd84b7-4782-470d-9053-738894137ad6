{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## DataTypes\n", "#### 1. Definition:\n", "\n", "- Data types are a classification of data which tell the compiler or interpreter how the programmer intends to use the data.\n", "- They determine the type of operations that can be performed on the data, the values that the data can take, and the amount of memory needed to store the data.\n", "\n", "#### 2. Importance of Data Types in Programming\n", "Explanation:\n", "\n", "- Data types ensure that data is stored in an efficient way.\n", "- They help in performing correct operations on data.\n", "- Proper use of data types can prevent errors and bugs in the program."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Video Outline:\n", "1. Introduction to Data Types\n", "2. Importance of Data Types in Programming\n", "3. Basic Data Types\n", "   - Integers\n", "   - Floating-point numbers\n", "   - Strings\n", "   - Booleans\n", "4. Advanced Data Types\n", "   - Lists\n", "   - <PERSON><PERSON>\n", "   - Sets\n", "   - Dictionaries\n", "5. Type Conversion\n", "6. Practical Examples"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["int"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["## Integer Example\n", "age=35\n", "type(age)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5.11\n", "<class 'float'>\n"]}], "source": ["##floating point datatype\n", "height=5.11\n", "print(height)\n", "print(type(height))\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON>\n", "<class 'str'>\n"]}], "source": ["## string datatype example\n", "name=\"<PERSON><PERSON>\"\n", "print(name)\n", "print(type(name))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["bool"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["## boolean datatype\n", "is_true=True\n", "type(is_true)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["bool"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["a=10\n", "b=10\n", "\n", "type(a==b)\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "can only concatenate str (not \"int\") to str", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mTypeError\u001b[0m                                 Trace<PERSON> (most recent call last)", "Cell \u001b[1;32mIn[12], line 3\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m## common errors\u001b[39;00m\n\u001b[1;32m----> 3\u001b[0m result\u001b[38;5;241m=\u001b[39m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m<PERSON><PERSON>\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m5\u001b[39;49m\n", "\u001b[1;31mTypeError\u001b[0m: can only concatenate str (not \"int\") to str"]}], "source": ["## common errors\n", "\n", "result=\"Hello\" + 5\n", " "]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello5\n"]}], "source": ["result=\"Hello\" + str(5)\n", "print(result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}