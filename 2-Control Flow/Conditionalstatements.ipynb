{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### Conditional Statements (if, elif, else)\n", "Video Outline:\n", "1. Introduction to Conditional Statements\n", "2. if Statement\n", "3. else Statement\n", "4. elif <PERSON>ment\n", "5. Nested Conditional Statements\n", "6. Practical Examples\n", "7. Common Errors and Best Practices"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You are allowed to vote in the elections\n"]}], "source": ["## if statement\n", "age=20\n", "\n", "if age>=18:\n", "    print(\"You are allowed to vote in the elections\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["age>=18"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You are a minor\n"]}], "source": ["## else\n", "## The else statement executes a block of code if the condition in the if statement is False.\n", "\n", "age=16\n", "\n", "if age>=18:\n", "    print(\"You are eligible for voting\")\n", "else:\n", "    print(\"You are a minor\")\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You are a teenager\n"]}], "source": ["## elif\n", "## The elif statement allows you to check multiple conditions. It stands for \"else if\"\n", "\n", "age=17\n", "\n", "if age<13:\n", "    print(\"You are a child\")\n", "elif age<18:\n", "    print(\"You are a teenager\")\n", "else:\n", "    print(\"You are an adult\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The number is zero or negative\n"]}], "source": ["## Nested Condiitonal Statements\n", "\n", "# You can place one or more if, elif, or else statements inside another if, elif, or else statement to create nested conditional statements.\n", "\n", "## number even ,odd,negative\n", "\n", "num=int(input(\"Enter the number\"))\n", "\n", "if num>0:\n", "    print(\"The number is positive\")\n", "    if num%2==0:\n", "        print(\"The number is even\")\n", "    else:\n", "        print(\"The number is odd\")\n", "\n", "else:\n", "    print(\"The number is zero or negative\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2024 is a leap year\n"]}], "source": ["## Practical Examples\n", "\n", "## Determine if a year is a leap year using nested condition statement\n", "\n", "year=int(input(\"Enter the year\"))\n", "\n", "if year%4==0:\n", "    if year%100==0:\n", "        if year%400==0:\n", "            print(year,\"is a leap year\")\n", "        else:\n", "            print(year,\"is not a leap year\")\n", "    else:\n", "        print(year,\"is a leap year\")\n", "\n", "else:\n", "    print(year,\"is not a leap year\")\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Result: 36.0\n"]}], "source": ["## Assignment\n", "## Simple Calculator program\n", "# Take user input\n", "num1 = float(input(\"Enter first number: \"))\n", "num2 = float(input(\"Enter second number: \"))\n", "operation = input(\"Enter operation (+, -, *, /): \")\n", "\n", "# Perform the requested operation\n", "if operation == '+':\n", "    result = num1 + num2\n", "elif operation == '-':\n", "    result = num1 - num2\n", "elif operation == '*':\n", "    result = num1 * num2\n", "elif operation == '/':\n", "    if num2 != 0:\n", "        result = num1 / num2\n", "    else:\n", "        result = \"Error! Division by zero.\"\n", "else:\n", "    result = \"Invalid operation.\"\n", "\n", "print(\"Result:\", result)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["### Determine the ticket price based on age and whether the person is a student.\n", "# Ticket pricing based on age and student status\n", "\n", "# Take user input\n", "age = int(input(\"Enter your age: \"))\n", "is_student = input(\"Are you a student? (yes/no): \").lower()\n", "\n", "# Determine ticket price\n", "if age < 5:\n", "    price = \"Free\"\n", "elif age <= 12:\n", "    price = \"$10\"\n", "elif age <= 17:\n", "    if is_student == 'yes':\n", "        price = \"$12\"\n", "    else:\n", "        price = \"$15\"\n", "elif age <= 64:\n", "    if is_student == 'yes':\n", "        price = \"$18\"\n", "    else:\n", "        price = \"$25\"\n", "else:\n", "    price = \"$20\"\n", "\n", "print(\"Ticket Price:\", price)\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Complex Example 3: Employee Bonus Calculation\n", "\n", "Calculate an employee's bonus based on their performance rating and years of service."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Employee bonus calculation\n", "\n", "# Take user input\n", "years_of_service = int(input(\"Enter years of service: \"))\n", "performance_rating = float(input(\"Enter performance rating (1.0 to 5.0): \"))\n", "\n", "# Determine bonus percentage\n", "if performance_rating >= 4.5:\n", "    if years_of_service > 10:\n", "        bonus_percentage = 20\n", "    elif years_of_service > 5:\n", "        bonus_percentage = 15\n", "    else:\n", "        bonus_percentage = 10\n", "elif performance_rating >= 3.5:\n", "    if years_of_service > 10:\n", "        bonus_percentage = 15\n", "    elif years_of_service > 5:\n", "        bonus_percentage = 10\n", "    else:\n", "        bonus_percentage = 5\n", "else:\n", "    bonus_percentage = 0\n", "\n", "# Calculate bonus amount\n", "salary = float(input(\"Enter current salary: \"))\n", "bonus_amount = salary * bonus_percentage / 100\n", "\n", "print(\"Bonus Amount: ${:.2f}\".format(bonus_amount))\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Complex Example 4: User Login System\n", "A simple user login system that checks the username and password."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON><PERSON> not found.\n"]}], "source": ["# User login system\n", "\n", "# Predefined username and password\n", "stored_username = \"admin\"\n", "stored_password = \"password123\"\n", "\n", "# Take user input\n", "username = input(\"Enter username: \")\n", "password = input(\"Enter password: \")\n", "\n", "# Check login credentials\n", "if username == stored_username:\n", "    if password == stored_password:\n", "        print(\"Login successful!\")\n", "    else:\n", "        print(\"Incorrect password.\")\n", "else:\n", "    print(\"<PERSON><PERSON><PERSON> not found.\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}