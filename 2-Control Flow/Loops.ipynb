{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### Loops\n", "Video Outline:\n", "1. Introduction to Loops\n", "2. for Loop\n", "   - Iterating over a range\n", "   - Iterating over a string\n", "\n", "3. while Loop\n", "4. Loop Control Statements\n", "    - break\n", "    - continue\n", "    - pass\n", "5. Nested <PERSON>s\n", "6. Practical Examples and Common Errors"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["range(0, 5)"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["range(5)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n", "1\n", "2\n", "3\n", "4\n"]}], "source": ["## for loop\n", "\n", "for i in range(5):\n", "    print(i)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "2\n", "3\n", "4\n", "5\n"]}], "source": ["for i in range(1,6):\n", "    print(i)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "3\n", "5\n", "7\n", "9\n"]}], "source": ["for i in range(1,10,2):\n", "    print(i)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["10\n", "9\n", "8\n", "7\n", "6\n", "5\n", "4\n", "3\n", "2\n"]}], "source": ["for i in range(10,1,-1):\n", "    print(i)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["10\n", "8\n", "6\n", "4\n", "2\n"]}], "source": ["for i in range(10,1,-2):\n", "    print(i)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["K\n", "r\n", "i\n", "s\n", "h\n", " \n", "N\n", "a\n", "i\n", "k\n"]}], "source": ["## strings\n", "\n", "str=\"<PERSON><PERSON> Na<PERSON>\"\n", "\n", "for i in str:\n", "    print(i)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n", "1\n", "2\n", "3\n", "4\n"]}], "source": ["## while loop\n", "\n", "## The while loop continues to execute as long as the condition is True.\n", "\n", "count=0\n", "\n", "while count<5:\n", "    print(count)\n", "    count=count+1\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n", "1\n", "2\n", "3\n", "4\n"]}], "source": ["## Loop Control Statements\n", "\n", "## break\n", "## The break statement exits the loop permaturely\n", "\n", "## break sstatement\n", "\n", "for i in range(10):\n", "    if i==5:\n", "        break\n", "    print(i)\n", "   "]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "3\n", "5\n", "7\n", "9\n"]}], "source": ["## continue\n", "\n", "## The continue statement skips the current iteration and continues with the next.\n", "\n", "for i in range(10):\n", "    if i%2==0:\n", "        continue\n", "    print(i)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n", "1\n", "2\n", "3\n", "4\n"]}], "source": ["## pass\n", "## The pass statement is a null operation; it does nothing.\n", "\n", "for i in range(5):\n", "    if i==3:\n", "        pass\n", "    print(i)\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["i:0 and j:0\n", "i:0 and j:1\n", "i:1 and j:0\n", "i:1 and j:1\n", "i:2 and j:0\n", "i:2 and j:1\n"]}], "source": ["## Nested loopss\n", "## a loop inside a loop\n", "\n", "for i in range(3):\n", "    for j in range(2):\n", "        print(f\"i:{i} and j:{j}\")"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sum of first 10 natural number: 55\n"]}], "source": ["## Examples- Calculate the sum of first N natural numbers using a while and for loop\n", "\n", "## while loop  \n", "\n", "n=10   \n", "sum=0\n", "count=1\n", "\n", "while count<=n:\n", "    sum=sum+count\n", "    count=count+1\n", "\n", "print(\"Sum of first 10 natural number:\",sum)\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["55\n"]}], "source": ["n=10   \n", "sum=0\n", "for i in range(11):\n", "    sum=sum+i\n", "\n", "print(sum)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2\n", "3\n", "5\n", "7\n", "11\n", "13\n", "17\n", "19\n", "23\n", "29\n", "31\n", "37\n", "41\n", "43\n", "47\n", "53\n", "59\n", "61\n", "67\n", "71\n", "73\n", "79\n", "83\n", "89\n", "97\n"]}], "source": ["## Example- Prime numbers between 1 and 100\n", "\n", "for num in range(1,101):\n", "    if num>1:\n", "        for i in range(2,num):\n", "            if num%i==0:\n", "                break\n", "        else:\n", "            print(num)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Conclusion:\n", "Loops are powerful constructs in Python that allow you to execute a block of code multiple times. By understanding and using for and while loops, along with loop control statements like break, continue, and pass, you can handle a wide range of programming tasks efficiently."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}