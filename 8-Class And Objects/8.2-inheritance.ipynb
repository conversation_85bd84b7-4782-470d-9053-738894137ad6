{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### Inheritance In Python\n", "Inheritance is a fundamental concept in Object-Oriented Programming (OOP) that allows a class to inherit attributes and methods from another class. This lesson covers single inheritance and multiple inheritance, demonstrating how to create and use them in Python."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["## Inheritance (Single Inheritance)\n", "## Parent class\n", "class Car:\n", "    def __init__(self,windows,doors,enginetype):\n", "        self.windows=windows\n", "        self.doors=doors\n", "        self.enginetype=enginetype\n", "    \n", "    def drive(self):\n", "        print(f\"The person will drive the {self.enginetype} car \")\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The person will drive the petrol car \n"]}], "source": ["car1=Car(4,5,\"petrol\")\n", "car1.drive()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["class Tesla(Car):\n", "    def __init__(self,windows,doors,enginetype,is_selfdriving):\n", "        super().__init__(windows,doors,enginetype)\n", "        self.is_selfdriving=is_selfdriving\n", "\n", "    def selfdriving(self):\n", "        print(f\"Tesla supports self driving : {self.is_selfdriving}\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tesla supports self driving : True\n"]}], "source": ["tesla1=Tesla(4,5,\"electric\",True)\n", "tesla1.selfdriving()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The person will drive the electric car \n"]}], "source": ["tesla1.drive()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Buddy say woof\n", "Owner:<PERSON><PERSON>\n"]}], "source": ["### Multiple Inheritance\n", "## When a class inherits from more than one base class.\n", "## Base class 1\n", "class Animal:\n", "    def __init__(self,name):\n", "        self.name=name\n", "\n", "    def speak(self):\n", "        print(\"Subclass must implement this method\")\n", "\n", "## BAse class 2\n", "class Pet:\n", "    def __init__(self, owner):\n", "        self.owner = owner\n", "\n", "\n", "##Derived class\n", "class Dog(Animal,Pet):\n", "    def __init__(self,name,owner):\n", "        Animal.__init__(self,name)\n", "        Pet.__init__(self,owner)\n", "\n", "    def speak(self):\n", "        return f\"{self.name} say woof\"\n", "    \n", "\n", "## Create an object\n", "dog=Dog(\"<PERSON>\",\"<PERSON><PERSON>\")\n", "print(dog.speak())\n", "print(f\"Owner:{dog.owner}\")\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Conclusion\n", "Inheritance is a powerful feature in OOP that allows for code reuse and the creation of a more logical class structure. Single inheritance involves one base class, while multiple inheritance involves more than one base class. Understanding how to implement and use inheritance in Python will enable you to design more efficient and maintainable object-oriented programs."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}