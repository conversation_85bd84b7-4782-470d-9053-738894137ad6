#!/usr/bin/env python
# coding: utf-8

# #### Abstraction
# Abstraction is the concept of hiding the complex implementation details and showing only the necessary features of an object. This helps in reducing programming complexity and effort.

# In[3]:


from abc import ABC,abstractmethod

## Abstract base cclass
class Vehicle(ABC):
    def drive(self):
        print("The vehicle is used for driving")

    @abstractmethod
    def start_engine(self):
        pass

class Car(Vehicle):
    def start_engine(self):
        print("Car enginer started")

def operate_vehicle(vehicle):
    vehicle.start_engine()
    vehicle.drive()

car=Car()
operate_vehicle(car)



# In[ ]:





# In[ ]:





# In[ ]:





# In[ ]:





# In[ ]:




