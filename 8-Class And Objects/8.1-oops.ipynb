{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["####  Classes and Objects\n", "Object-Oriented Programming (OOP) is a programming paradigm that uses \"objects\" to design applications and computer programs. OOP allows for modeling real-world scenarios using classes and objects. This lesson covers the basics of creating classes and objects, including instance variables and methods."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class '__main__.Car'>\n"]}], "source": ["### A class is a blue print for creating objects. Attributes,methods\n", "class Car:\n", "    pass\n", "\n", "audi=Car()\n", "bmw=Car()\n", "\n", "print(type(audi))\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<__main__.Car object at 0x0000015A0B79FF20>\n", "<__main__.Car object at 0x0000015A0A3374A0>\n"]}], "source": ["print(audi)\n", "print(bmw)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4\n"]}], "source": ["audi.windows=4\n", "\n", "print(audi.windows)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'Car' object has no attribute 'windows'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[5], line 3\u001b[0m\n\u001b[0;32m      1\u001b[0m tata\u001b[38;5;241m=\u001b[39mCar()\n\u001b[0;32m      2\u001b[0m tata\u001b[38;5;241m.\u001b[39mdoors\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m4\u001b[39m\n\u001b[1;32m----> 3\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[43mtata\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mwindows\u001b[49m)\n", "\u001b[1;31mAttributeError\u001b[0m: 'Car' object has no attribute 'windows'"]}], "source": ["tata=Car()\n", "tata.doors=4\n", "print(tata.windows)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["['__class__',\n", " '__delattr__',\n", " '__dict__',\n", " '__dir__',\n", " '__doc__',\n", " '__eq__',\n", " '__format__',\n", " '__ge__',\n", " '__getattribute__',\n", " '__getstate__',\n", " '__gt__',\n", " '__hash__',\n", " '__init__',\n", " '__init_subclass__',\n", " '__le__',\n", " '__lt__',\n", " '__module__',\n", " '__ne__',\n", " '__new__',\n", " '__reduce__',\n", " '__reduce_ex__',\n", " '__repr__',\n", " '__setattr__',\n", " '__sizeof__',\n", " '__str__',\n", " '__subclasshook__',\n", " '__weakref__',\n", " 'doors']"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["dir(tata)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<__main__.Dog object at 0x0000015A0B7E99D0>\n", "<PERSON>\n", "3\n"]}], "source": ["### Instance Variable and Methods\n", "class Dog:\n", "    ## constructor\n", "    def __init__(self,name,age):\n", "        self.name=name\n", "        self.age=age\n", "\n", "## create objects\n", "dog1=<PERSON>(\"<PERSON>\",3)\n", "print(dog1)\n", "print(dog1.name)\n", "print(dog1.age)\n", "    \n", "    "]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON>\n", "4\n"]}], "source": ["dog2=<PERSON>(\"<PERSON>\",4)\n", "print(dog2.name)\n", "print(dog2.age)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON> says woof\n", "<PERSON> says woof\n"]}], "source": ["## Define a class with instance methods\n", "class Dog:\n", "    def __init__(self,name,age):\n", "        self.name=name\n", "        self.age=age\n", "    \n", "    def bark(self):\n", "        print(f\"{self.name} says woof\")\n", "\n", "\n", "dog1=<PERSON>(\"<PERSON>\",3)\n", "dog1.bark()\n", "dog2=<PERSON>(\"<PERSON>\",4)\n", "dog2.bark()\n", "\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5000\n"]}], "source": ["### Modeling a Bank Account\n", "\n", "## Define a class for bank account\n", "class BankAccount:\n", "    def __init__(self,owner,balance=0):\n", "        self.owner=owner\n", "        self.balance=balance\n", "\n", "    def deposit(self,amount):\n", "        self.balance+=amount\n", "        print(f\"{amount} is deposited. New balance is {self.balance}\")\n", "\n", "    def withdraw(self,amount):\n", "        if amount>self.balance:\n", "            print(\"Insufficient funds!\")\n", "        else:\n", "            self.balance-=amount\n", "            print(f\"{amount} is withdrawn. New Balance is {self.balance}\")\n", "\n", "    def get_balance(self):\n", "        return self.balance\n", "    \n", "## create an account\n", "\n", "account=BankAccount(\"<PERSON><PERSON>\",5000)\n", "print(account.balance)\n", "\n", "    "]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["100 is deposited. New balance is 5100\n"]}], "source": ["## Call isntance methods\n", "account.deposit(100)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["300 is withdrawn. New Balance is 4800\n"]}], "source": ["account.withdraw(300)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4800\n"]}], "source": ["print(account.get_balance())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Conclusion\n", "Object-Oriented Programming (OOP) allows you to model real-world scenarios using classes and objects. In this lesson, you learned how to create classes and objects, define instance variables and methods, and use them to perform various operations. Understanding these concepts is fundamental to writing effective and maintainable Python code."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}