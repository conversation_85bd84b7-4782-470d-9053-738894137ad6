{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### Encapsulation And Abstraction\n", "Encapsulation and abstraction are two fundamental principles of Object-Oriented Programming (OOP) that help in designing robust, maintainable, and reusable code. Encapsulation involves bundling data and methods that operate on the data within a single unit, while abstraction involves hiding complex implementation details and exposing only the necessary features."]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Encapsulation\n", "Encapsulation is the concept of wrapping data (variables) and methods (functions) together as a single unit. It restricts direct access to some of the object's components, which is a means of preventing accidental interference and misuse of the data.\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["'<PERSON><PERSON>'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["### Encapsulation  with <PERSON><PERSON> and <PERSON><PERSON> MEthods\n", "### Public,protected,private variables or access modifiers\n", "\n", "class Person:\n", "    def __init__(self,name,age):\n", "        self.name=name    ## public variables\n", "        self.age=age      ## public variables\n", "\n", "def get_name(person):\n", "    return person.name\n", "\n", "person=Person(\"<PERSON><PERSON>\",34)\n", "get_name(person)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["['__class__',\n", " '__delattr__',\n", " '__dict__',\n", " '__dir__',\n", " '__doc__',\n", " '__eq__',\n", " '__format__',\n", " '__ge__',\n", " '__getattribute__',\n", " '__getstate__',\n", " '__gt__',\n", " '__hash__',\n", " '__init__',\n", " '__init_subclass__',\n", " '__le__',\n", " '__lt__',\n", " '__module__',\n", " '__ne__',\n", " '__new__',\n", " '__reduce__',\n", " '__reduce_ex__',\n", " '__repr__',\n", " '__setattr__',\n", " '__sizeof__',\n", " '__str__',\n", " '__subclasshook__',\n", " '__weakref__',\n", " 'age',\n", " 'name']"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["dir(person)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'Person' object has no attribute '__name'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[15], line 11\u001b[0m\n\u001b[0;32m      8\u001b[0m     \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m person\u001b[38;5;241m.\u001b[39m__name\n\u001b[0;32m     10\u001b[0m person\u001b[38;5;241m=\u001b[39mPerson(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m<PERSON><PERSON><PERSON>\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;241m34\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMale\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m---> 11\u001b[0m \u001b[43mget_name\u001b[49m\u001b[43m(\u001b[49m\u001b[43<PERSON><PERSON>\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[1;32mIn[15], line 8\u001b[0m, in \u001b[0;36mget_name\u001b[1;34m(person)\u001b[0m\n\u001b[0;32m      7\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mget_name\u001b[39m(person):\n\u001b[1;32m----> 8\u001b[0m     \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[43<PERSON><PERSON>\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m__name\u001b[49m\n", "\u001b[1;31mAttributeError\u001b[0m: 'Person' object has no attribute '__name'"]}], "source": ["class Person:\n", "    def __init__(self,name,age,gender):\n", "        self.__name=name    ## private variables\n", "        self.__age=age      ## private variables\n", "        self.gender=gender\n", "\n", "def get_name(person):\n", "    return person.__name\n", "\n", "person=Person(\"<PERSON><PERSON>\",34,\"<PERSON>\")\n", "get_name(person)\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["['_Person__age',\n", " '_Person__name',\n", " '__class__',\n", " '__delattr__',\n", " '__dict__',\n", " '__dir__',\n", " '__doc__',\n", " '__eq__',\n", " '__format__',\n", " '__ge__',\n", " '__getattribute__',\n", " '__getstate__',\n", " '__gt__',\n", " '__hash__',\n", " '__init__',\n", " '__init_subclass__',\n", " '__le__',\n", " '__lt__',\n", " '__module__',\n", " '__ne__',\n", " '__new__',\n", " '__reduce__',\n", " '__reduce_ex__',\n", " '__repr__',\n", " '__setattr__',\n", " '__sizeof__',\n", " '__str__',\n", " '__subclasshook__',\n", " '__weakref__',\n", " 'gender']"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["dir(person)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["K<PERSON>ish\n"]}], "source": ["class Person:\n", "    def __init__(self,name,age,gender):\n", "        self._name=name    ## protected variables\n", "        self._age=age      ## protected variables\n", "        self.gender=gender\n", "\n", "class Employee(Person):\n", "    def __init__(self,name,age,gender):\n", "        super().__init__(name,age,gender)\n", "\n", "\n", "employee=Employee(\"KR<PERSON>\",34,\"Male\")\n", "print(employee._name)\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON>\n", "34\n", "35\n", "Age cannot be negative.\n"]}], "source": ["## Encapsulation With Getter And Setter\n", "class Person:\n", "    def __init__(self,name,age):\n", "        self.__name=name  ## Private access modifier or variable\n", "        self.__age=age ## Private variable\n", "\n", "    ## getter method for name\n", "    def get_name(self):\n", "        return self.__name\n", "    \n", "    ## setter method for name\n", "    def set_name(self,name):\n", "        self.__name=name\n", "\n", "    # Getter method for age\n", "    def get_age(self):\n", "        return self.__age\n", "    \n", "    # Setter method for age\n", "    def set_age(self, age):\n", "        if age > 0:\n", "            self.__age = age\n", "        else:\n", "            print(\"Age cannot be negative.\")\n", "\n", "\n", "person=Person(\"<PERSON><PERSON>\",34)\n", "\n", "## Access and modify private variables using getter and setter\n", "\n", "print(person.get_name())\n", "print(person.get_age())\n", "\n", "person.set_age(35)\n", "print(person.get_age())\n", "\n", "person.set_age(-5)\n", "    \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}