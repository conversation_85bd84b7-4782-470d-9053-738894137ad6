{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["##### Operator Overloading\n", "Operator overloading allows you to define the behavior of operators (+, -, *, etc.) for custom objects. You achieve this by overriding specific magic methods in your class."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\n__add__(self, other): Adds two objects using the + operator.\\n__sub__(self, other): Subtracts two objects using the - operator.\\n__mul__(self, other): Multiplies two objects using the * operator.\\n__truediv__(self, other): Divides two objects using the / operator.\\n__eq__(self, other): Checks if two objects are equal using the == operator.\\n__lt__(self, other): Checks if one object is less than another using the < operator.\\n\\n'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["#### Common Operator Overloading Magic Methods\n", "'''\n", "__add__(self, other): Adds two objects using the + operator.\n", "__sub__(self, other): Subtracts two objects using the - operator.\n", "__mul__(self, other): Multiplies two objects using the * operator.\n", "__truediv__(self, other): Divides two objects using the / operator.\n", "__eq__(self, other): Checks if two objects are equal using the == operator.\n", "__lt__(self, other): Checks if one object is less than another using the < operator.\n", "\n", "__gt__\n", "'''"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Vector(6, 8)\n", "Vector(-2, -2)\n", "Vector(6, 9)\n"]}], "source": ["### MAthematical operation for vectors\n", "class Vector:\n", "    def __init__(self,x,y):\n", "        self.x=x\n", "        self.y=y\n", "\n", "    def __add__(self,other):\n", "        return Vector(self.x+other.x,self.y+other.y)\n", "    \n", "    def __sub__(self, other):\n", "        return Vector(self.x - other.x, self.y - other.y)\n", "\n", "    def __mul__(self, other):\n", "        return Vector(self.x * other, self.y * other)\n", "    \n", "    def __eq__(self, other):\n", "        return self.x == other.x and self.y == other.y\n", "    \n", "    def __repr__(self):\n", "        return f\"Vector({self.x}, {self.y})\"\n", "    \n", "## create objectss of the Vector Class\n", "v1=Vector(2,3)\n", "v2=Vector(4,5)\n", "\n", "print(v1+v2)\n", "print(v1-v2)\n", "print(v1*3)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### Overloading Operators for Complex Numbers\n", "\n", "class ComplexNumber:\n", "    def __init__(self, real, imag):\n", "        self.real = real\n", "        self.imag = imag\n", "\n", "    def __add__(self, other):\n", "        return ComplexNumber(self.real + other.real, self.imag + other.imag)\n", "\n", "    def __sub__(self, other):\n", "        return ComplexNumber(self.real - other.real, self.imag - other.imag)\n", "\n", "    def __mul__(self, other):\n", "        real_part = self.real * other.real - self.imag * other.imag\n", "        imag_part = self.real * other.imag + self.imag * other.real\n", "        return ComplexNumber(real_part, imag_part)\n", "\n", "    def __truediv__(self, other):\n", "        denominator = other.real**2 + other.imag**2\n", "        real_part = (self.real * other.real + self.imag * other.imag) / denominator\n", "        imag_part = (self.imag * other.real - self.real * other.imag) / denominator\n", "        return ComplexNumber(real_part, imag_part)\n", "\n", "    def __eq__(self, other):\n", "        return self.real == other.real and self.imag == other.imag\n", "\n", "    def __repr__(self):\n", "        return f\"{self.real} + {self.imag}i\"\n", "\n", "# Create objects of the ComplexNumber class\n", "c1 = ComplexNumber(2, 3)\n", "c2 = ComplexNumber(1, 4)\n", "\n", "# Use overloaded operators\n", "print(c1 + c2)  # Output: 3 + 7i\n", "print(c1 - c2)  # Output: 1 - 1i\n", "print(c1 * c2)  # Output: -10 + 11i\n", "print(c1 / c2)  # Output: 0.8235294117647058 - 0.29411764705882354i\n", "print(c1 == c2) # Output: False\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}