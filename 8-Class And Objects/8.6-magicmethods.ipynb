{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### Magic Methods\n", "Magic methods in Python, also known as dunder methods (double underscore methods), are special methods that start and end with double underscores. These methods enable you to define the behavior of objects for built-in operations, such as arithmetic operations, comparisons, and more."]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Magic Methods\n", "Magic methods are predefined methods in Python that you can override to change the behavior of your objects. Some common magic methods include:\n", "\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["\"\\n__init__': Initializes a new instance of a class.\\n__str__: Returns a string representation of an object.\\n__repr__: Returns an official string representation of an object.\\n__len__: Returns the length of an object.\\n__getitem__: Gets an item from a container.\\n__setitem__: Sets an item in a container.\\n\""]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["'''\n", "__init__': Initializes a new instance of a class.\n", "__str__: Returns a string representation of an object.\n", "__repr__: Returns an official string representation of an object.\n", "__len__: Returns the length of an object.\n", "__getitem__: Gets an item from a container.\n", "__setitem__: Sets an item in a container.\n", "'''"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["['__class__',\n", " '__delattr__',\n", " '__dict__',\n", " '__dir__',\n", " '__doc__',\n", " '__eq__',\n", " '__format__',\n", " '__ge__',\n", " '__getattribute__',\n", " '__getstate__',\n", " '__gt__',\n", " '__hash__',\n", " '__init__',\n", " '__init_subclass__',\n", " '__le__',\n", " '__lt__',\n", " '__module__',\n", " '__ne__',\n", " '__new__',\n", " '__reduce__',\n", " '__reduce_ex__',\n", " '__repr__',\n", " '__setattr__',\n", " '__sizeof__',\n", " '__str__',\n", " '__subclasshook__',\n", " '__weakref__']"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["class Person:\n", "    pass\n", "\n", "person=Person()\n", "dir(person)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<__main__.Person object at 0x0000029E5D059940>\n"]}], "source": ["print(person)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<__main__.Person object at 0x0000029E5D31D4C0>\n"]}], "source": ["## Basics MEthods\n", "class Person:\n", "    def __init__(self,name,age):\n", "        self.name=name\n", "        self.age=age\n", "person=Person(\"KR<PERSON>\",34)\n", "print(person)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON><PERSON>,34 years old\n", "Person(name=<PERSON><PERSON>ish,age=34)\n"]}], "source": ["## Basics MEthods\n", "class Person:\n", "    def __init__(self,name,age):\n", "        self.name=name\n", "        self.age=age\n", "    \n", "    def __str__(self):\n", "        return f\"{self.name},{self.age} years old\"\n", "    \n", "    def __repr__(self):\n", "        return f\"Person(name={self.name},age={self.age})\"\n", "    \n", "person=Person(\"KR<PERSON>\",34)\n", "print(person)\n", "print(repr(person))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}