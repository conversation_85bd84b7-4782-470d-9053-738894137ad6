{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### Abstraction\n", "Abstraction is the concept of hiding the complex implementation details and showing only the necessary features of an object. This helps in reducing programming complexity and effort."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Car enginer started\n", "The vehicle is used for driving\n"]}], "source": ["from abc import ABC,abstractmethod\n", "\n", "## Abstract base cclass\n", "class Vehicle(ABC):\n", "    def drive(self):\n", "        print(\"The vehicle is used for driving\")\n", "\n", "    @abstractmethod\n", "    def start_engine(self):\n", "        pass\n", "\n", "class Car(Vehicle):\n", "    def start_engine(self):\n", "        print(\"Car enginer started\")\n", "\n", "def operate_vehicle(vehicle):\n", "    vehicle.start_engine()\n", "    vehicle.drive()\n", "\n", "car=Car()\n", "operate_vehicle(car)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}