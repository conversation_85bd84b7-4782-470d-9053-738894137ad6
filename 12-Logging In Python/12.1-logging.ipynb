{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### Python Logging\n", "Logging is a crucial aspect of any application, providing a way to track events, errors, and operational information. Python's built-in logging module offers a flexible framework for emitting log messages from Python programs. In this lesson, we will cover the basics of logging, including how to configure logging, log levels, and best practices for using logging in Python applications."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-06-19 12:14:39 - root - DEBUG - This is a debug message\n", "2024-06-19 12:14:39 - root - INFO - This is an info message\n", "2024-06-19 12:14:39 - root - WARNING - This is a warning message\n", "2024-06-19 12:14:39 - root - ERROR - This is an error message\n", "2024-06-19 12:14:39 - root - CRITICAL - This is a critical message\n"]}], "source": ["import logging\n", "\n", "## Configure the basic logging settings\n", "logging.basicConfig(level=logging.DEBUG)\n", "\n", "## log messages with different severity levels\n", "logging.debug(\"This is a debug message\")\n", "logging.info(\"This is an info message\")\n", "logging.warning(\"This is a warning message\")\n", "logging.error(\"This is an error message\")\n", "logging.critical(\"This is a critical message\")\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Log Levels\n", "Python's logging module has several log levels indicating the severity of events. The default levels are:\n", "\n", "- DEBUG: Detailed information, typically of interest only when diagnosing problems.\n", "- INFO: Confirmation that things are working as expected.\n", "- WARNING: An indication that something unexpected happened or indicative of some problem in the near future (e.g., ‘disk space low’). The software is still working as expected.\n", "- ERROR: Due to a more serious problem, the software has not been able to perform some function.\n", "- CRITICAL: A very serious error, indicating that the program itself may be unable to continue running."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["\n", "## configuring logging\n", "import logging\n", "\n", "logging.basicConfig(\n", "    filename='app.log',\n", "    filemode='w',\n", "    level=logging.DEBUG,\n", "    format='%(asctime)s-%(name)s-%(levelname)s-%(message)s',\n", "    datefmt='%Y-%m-%d %H:%M:%S'\n", "    )\n", "\n", "## log messages with different severity levels\n", "logging.debug(\"This is a debug message\")\n", "logging.info(\"This is an info message\")\n", "logging.warning(\"This is a warning message\")\n", "logging.error(\"This is an error message\")\n", "logging.critical(\"This is a critical message\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["logging.debug(\"This is a debug message\")\n", "logging.info(\"This is an info message\")\n", "logging.warning(\"This is a warning message\")\n", "logging.error(\"This is an error message\")\n", "logging.critical(\"This is a critical message\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}