{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### Logging with Multiple Loggers\n", "You can create multiple loggers for different parts of your application."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import logging\n", "## create a logger for module1\n", "logger1=logging.getLogger(\"module1\")\n", "logger1.setLevel(logging.DEBUG)\n", "\n", "##create a logger for module 2\n", "\n", "logger2=logging.getLogger(\"module2\")\n", "logger2.setLevel(logging.WARNING)\n", "\n", "# Configure logging settings\n", "logging.basicConfig(\n", "    level=logging.DEBUG,\n", "    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',\n", "    datefmt='%Y-%m-%d %H:%M:%S'\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-06-19 13:21:55 - module1 - DEBUG - This is debug message for module1\n", "2024-06-19 13:21:55 - module2 - WARNING - This is a warning message for module 2\n", "2024-06-19 13:21:55 - module2 - ERROR - This is an error message\n"]}], "source": ["## log message with different loggers\n", "logger1.debug(\"This is debug message for module1\")\n", "logger2.warning(\"This is a warning message for module 2\")\n", "logger2.error(\"This is an error message\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}