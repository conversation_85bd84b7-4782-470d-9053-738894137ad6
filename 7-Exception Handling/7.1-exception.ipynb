{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### Understanding Exceptions\n", "\n", "Exception handling in Python allows you to handle errors gracefully and take corrective actions without stopping the execution of the program. This lesson will cover the basics of exceptions, including how to use try, except, else, and finally blocks."]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### What Are Exceptions?\n", "Exceptions are events that disrupt the normal flow of a program. They occur when an error is encountered during program execution. Common exceptions include:\n", "\n", "- ZeroDivisionError: Dividing by zero.\n", "- FileNotFoundError: File not found.\n", "- ValueError: Invalid value.\n", "- TypeError: Invalid type."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The variable has not been assigned\n"]}], "source": ["## Exception try ,except block\n", "\n", "try:\n", "    a=b\n", "except:\n", "    print(\"The variable has not been assigned\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'b' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[4], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m a\u001b[38;5;241m=\u001b[39m\u001b[43mb\u001b[49m\n", "\u001b[1;31mNameError\u001b[0m: name 'b' is not defined"]}], "source": ["a=b"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["name 'b' is not defined\n"]}], "source": ["try:\n", "    a=b\n", "except NameError as ex:\n", "    print(ex)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["division by zero\n", "Please enter the denominator greater than 0\n"]}], "source": ["try:\n", "    result=1/0\n", "except ZeroDivisionError as ex:\n", "    print(ex)\n", "    print(\"Please enter the denominator greater than 0\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["name 'b' is not defined\n", "Main exception got caught here\n"]}], "source": ["try:\n", "    result=1/2\n", "    a=b\n", "except ZeroDivisionError as ex:\n", "    print(ex)\n", "    print(\"Please enter the denominator greater than 0\")\n", "except Exception as ex1:\n", "    print(ex1)\n", "    print('Main exception got caught here')"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["try:\n", "    num=int(input(\"Enter a number\"))\n", "    result=10/num\n", "except ValueError:\n", "    print(\"This is not a valid number\")\n", "except ZeroDivisionError:\n", "    print(\"enter denominator greater than 0\")\n", "except Exception as ex:\n", "    print(ex)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You can't divide by zero!\n"]}], "source": ["## try,except,else block\n", "try:\n", "    num=int(input(\"Enter a number:\"))\n", "    result=10/num\n", "except ValueError:\n", "    print(\"That's not a valid number!\")\n", "except ZeroDivisionError:\n", "    print(\"You can't divide by zero!\")\n", "except Exception as ex:\n", "    print(ex)\n", "else:\n", "    print(f\"the result is {result}\")\n", "\n", "    \n", "\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You can't divide by zero!\n", "Execution complete.\n"]}], "source": ["## try,except,else and finally\n", "try:\n", "    num = int(input(\"Enter a number: \"))\n", "    result = 10 / num\n", "except ValueError:\n", "    print(\"That's not a valid number!\")\n", "except ZeroDivisionError:\n", "    print(\"You can't divide by zero!\")\n", "except Exception as ex:\n", "    print(ex)\n", "else:\n", "    print(f\"The result is {result}\")\n", "finally:\n", "    print(\"Execution complete.\")\n", "\n"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["name 'b' is not defined\n", "file close\n"]}], "source": ["### File handling and Exception HAndling\n", "\n", "try:\n", "    file=open('example1.txt','r')\n", "    content=file.read()\n", "    a=b\n", "    print(content)\n", "\n", "except FileNotFoundError:\n", "    print(\"The file does not exists\")\n", "except Exception as ex:\n", "    print(ex)\n", "\n", "finally:\n", "    if 'file' in locals() or not file.closed():\n", "        file.close()\n", "        print('file close')"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n"]}], "source": ["if 'file' in locals():\n", "    print(True)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "'bool' object is not callable", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mTypeError\u001b[0m                                 Trace<PERSON> (most recent call last)", "Cell \u001b[1;32mIn[24], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[43mfile\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mclosed\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[1;31mTypeError\u001b[0m: 'bool' object is not callable"]}], "source": ["not file.closed()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}