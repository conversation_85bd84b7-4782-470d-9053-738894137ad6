{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["##### Importing Modules in Python: Modules and Packages\n", "In Python, modules and packages help organize and reuse code. Here's a comprehensive guide on how to import them."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["4.0"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import math\n", "math.sqrt(16)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4.0\n", "5.0\n", "3.141592653589793\n"]}], "source": ["from math import sqrt,pi\n", "print(sqrt(16))\n", "print(sqrt(25))\n", "print(pi)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1, 2, 3, 4])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "np.array([1,2,3,4])"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4.0\n", "3.141592653589793\n"]}], "source": ["from math import *\n", "print(sqrt(16))\n", "print(pi)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["5"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["from package.maths import addition\n", "addition(2,3)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["5"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["from package import maths\n", "maths.addition(2,3)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Conclusion\n", "Importing modules and packages in Python allows you to organize your code, reuse functionalities, and keep your projects clean and manageable. By understanding how to import modules, specific functions, and use relative imports within packages, you can structure your Python applications more effectively."]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}