{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["##### Standard Library Overview\n", "Python's Standard Library is a vast collection of modules and packages that come bundled with Python, providing a wide range of functionalities out of the box. Here's an overview of some of the most commonly used modules and packages in the Python Standard Library."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["array('i', [1, 2, 3, 4])\n"]}], "source": ["import array\n", "arr=array.array('i',[1,2,3,4])\n", "print(arr)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4.0\n", "3.141592653589793\n"]}], "source": ["import math\n", "print(math.sqrt(16))\n", "print(math.pi)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2\n", "cherry\n"]}], "source": ["## random \n", "\n", "import random\n", "print(random.randint(1,10))\n", "print(random.choice(['apple','banana','cherry']))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["e:\\UDemy Final\\python\\5-Modules\n"]}], "source": ["### File And Directory Access\n", "\n", "import os\n", "print(os.getcwd())"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["os.mkdir('test_dir')"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["'destination.txt'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["## High level operations on files and collection of files\n", "import shutil\n", "shutil.copyfile('source.txt','destination.txt')"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\"name\": \"<PERSON><PERSON>\", \"age\": 25}\n", "<class 'str'>\n", "{'name': '<PERSON><PERSON>', 'age': 25}\n", "<class 'dict'>\n"]}], "source": ["## Data Serialization\n", "import json\n", "data={'name':'<PERSON><PERSON>','age':25}\n", "\n", "json_str=json.dumps(data)\n", "print(json_str)\n", "print(type(json_str))\n", "\n", "parsed_data=json.loads(json_str)\n", "print(parsed_data)\n", "print(type(parsed_data))\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['name', 'age']\n", "['<PERSON><PERSON>', '32']\n"]}], "source": ["## csv\n", "\n", "import csv\n", "\n", "with open('example.csv',mode='w',newline='') as file:\n", "    writer=csv.writer(file)\n", "    writer.writerow(['name','age'])\n", "    writer.writerow(['<PERSON><PERSON>',32])\n", "\n", "with open('example.csv',mode='r') as file:\n", "    reader=csv.reader(file)\n", "    for row in reader:\n", "        print(row)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2024-06-11 11:37:28.084474\n", "2024-06-10 11:37:28.084474\n"]}], "source": ["## datetime\n", "from datetime import datetime,timedelta\n", "\n", "now=datetime.now()\n", "print(now)\n", "\n", "yesterday=now-<PERSON><PERSON><PERSON>(days=1)\n", "\n", "print(yesterday)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1718086104.8242216\n", "1718086106.82563\n"]}], "source": ["## time\n", "import time\n", "print(time.time())\n", "time.sleep(2)\n", "print(time.time())"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["123\n"]}], "source": ["## Regular expresiion\n", "import re\n", "\n", "pattern=r'\\d+'\n", "text='There are 123 apples 456'\n", "match=re.search(pattern,text)\n", "print(match.group())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Conclusion\n", "Python's Standard Library is extensive and provides tools for almost any task you can think of, from file handling to web services, from data serialization to concurrent execution. Familiarizing yourself with the modules and packages available in the Standard Library can significantly enhance your ability to write efficient and effective Python programs."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}