{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### Functions in Python\n", "Video Outline:\n", "1. Introduction to Functions\n", "2. Defining Functions\n", "3. Calling Functions\n", "4. Function Parameters\n", "5. <PERSON><PERSON><PERSON> Parameters\n", "6. Variable-Length Arguments\n", "7. Return Statement"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Introduction to Functions\n", "Definition:\n", "\n", "A function is a block of code that performs a specific task.\n", "Functions help in organizing code, reusing code, and improving readability.\n", "\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["## syntax\n", "def function_name(parameters):\n", "    \"\"\"Docstring\"\"\"\n", "    # Function body\n", "    return expression\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["the number is even\n"]}], "source": ["## why functions?\n", "num=24\n", "if num%2==0:\n", "    print(\"the number is even\")\n", "else:\n", "    print(\"the number is odd\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def even_or_odd(num):\n", "    \"\"\"This function finds even or odd\"\"\"\n", "    if num%2==0:\n", "        print(\"the number is even\")\n", "    else:\n", "        print(\"the number is odd\")\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["the number is even\n"]}], "source": ["## Call this function\n", "even_or_odd(24)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["6\n"]}], "source": ["## function with multiple parameters\n", "\n", "def add(a,b):\n", "    return a+b\n", "\n", "result=add(2,4)\n", "print(result)\n", "    "]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello <PERSON>h Welcome To the paradise\n"]}], "source": ["## Default Parameters\n", "\n", "def greet(name):\n", "    print(f\"Hello {name} Welcome To the paradise\")\n", "\n", "greet(\"<PERSON><PERSON>\")\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello <PERSON>h Welcome To the paradise\n"]}], "source": ["def greet(name=\"Guest\"):\n", "    print(f\"Hello {name} Welcome To the paradise\")\n", "\n", "greet(\"<PERSON><PERSON>\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["### Variable Length Arguments\n", "## Positional And Keywords arguments\n", "\n", "def print_numbers(*krish):\n", "    for number in krish:\n", "        print(number)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "2\n", "3\n", "4\n", "5\n", "6\n", "7\n", "8\n", "<PERSON><PERSON>\n"]}], "source": ["print_numbers(1,2,3,4,5,6,7,8,\"<PERSON><PERSON>\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["## Positional arguments\n", "def print_numbers(*args):\n", "    for number in args:\n", "        print(number)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "2\n", "3\n", "4\n", "5\n", "6\n", "7\n", "8\n", "<PERSON><PERSON>\n"]}], "source": ["print_numbers(1,2,3,4,5,6,7,8,\"<PERSON><PERSON>\")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["### Keywords Arguments\n", "\n", "def print_details(**kwargs):\n", "    for key,value in kwargs.items():\n", "        print(f\"{key}:{value}\")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["name:<PERSON><PERSON>\n", "age:32\n", "country:India\n"]}], "source": ["print_details(name=\"<PERSON><PERSON>\",age=\"32\",country=\"India\")"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["def print_details(*args,**kwargs):\n", "    for val in args:\n", "        print(f\" Positional arument :{val}\")\n", "    \n", "    for key,value in kwargs.items():\n", "        print(f\"{key}:{value}\")"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Positional arument :1\n", " Positional arument :2\n", " Positional arument :3\n", " Positional arument :4\n", " Positional arument :<PERSON><PERSON>\n", "name:<PERSON><PERSON>\n", "age:32\n", "country:India\n"]}], "source": ["print_details(1,2,3,4,\"<PERSON><PERSON>\",name=\"<PERSON><PERSON>\",age=\"32\",country=\"India\")"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["6"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["### Return statements\n", "def multiply(a,b):\n", "    return a*b\n", "\n", "multiply(2,3)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["(6, 2)"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["### Return multiple parameters\n", "def multiply(a,b):\n", "    return a*b,a\n", "\n", "multiply(2,3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}