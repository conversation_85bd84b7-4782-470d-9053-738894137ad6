{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### Functions Examples\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Example 1: Temperature Conversion"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["77.0\n", "25.0\n"]}], "source": ["def convert_temperature(temp,unit):\n", "    \"\"\"This function converts temperature between Celsius and Fahrenheit\"\"\"\n", "    if unit=='C':\n", "        return temp * 9/5 + 32  ## <PERSON><PERSON><PERSON> To Fahrenheit\n", "    elif unit==\"F\":\n", "        return (temp-32)*5/9 ## <PERSON><PERSON><PERSON>he<PERSON> to celsius\n", "    else:\n", "        return None\n", "\n", "print(convert_temperature(25,'C'))\n", "print(convert_temperature(77,'F'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Example 2: Password Strength Checker"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False\n", "True\n"]}], "source": ["def is_strong_password(password):\n", "    \"\"\"This function checks if the password is strong or not\"\"\"\n", "    if len(password)<8:\n", "        return False\n", "    if not any(char.isdigit() for char in password):\n", "        return False\n", "    if not any(char.islower() for char in password):\n", "        return False\n", "    if not any(char.isupper() for char in password):\n", "        return False\n", "    if not any(char in '!@#$%^&*()_+' for char in password):\n", "        return False\n", "    return True\n", "\n", "## calling the function\n", "print(is_strong_password(\"WeakPwd\"))\n", "print(is_strong_password(\"Str0ngPwd!\"))\n", "    \n", "    "]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Example 3: Calculate the Total Cost Of Items In a Shopping Cart"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5.8999999999999995\n"]}], "source": ["def calculate_total_cost(cart):\n", "    total_cost=0\n", "    for item in cart:\n", "        total_cost+=item['price']* item['quantity']\n", "\n", "    return total_cost\n", "\n", "\n", "## Example cart data\n", "\n", "cart=[\n", "    {'name':'Apple','price':0.5,'quantity':4},\n", "    {'name':'<PERSON><PERSON>','price':0.3,'quantity':6},\n", "    {'name':'Orange','price':0.7,'quantity':3}\n", "\n", "]\n", "\n", "## calling the function\n", "total_cost=calculate_total_cost(cart)\n", "print(total_cost)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Example 4: Check IF a String Is Palindrome"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n", "False\n"]}], "source": ["def is_palindrome(s):\n", "    s=s.lower().replace(\" \",\"\")\n", "    return s==s[::-1]\n", "\n", "print(is_palindrome(\"A man a plan a canal Panama\"))\n", "print(is_palindrome(\"Hello\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Example 5: Calculate the factorials of a number using recursion"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["720\n"]}], "source": ["def factorial(n):\n", "    if n==0:\n", "        return 1\n", "    else:\n", "        return n * factorial(n-1)\n", "    \n", "print(factorial(6))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Example 6: A Function To Read A File and count the frequency of each word"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'hello': 1, 'world': 1, 'how': 1, 'are': 1, 'you': 1, 'my': 1, 'name': 1, 'is': 1, 'krish': 2}\n"]}], "source": ["def count_word_frequency(file_path):\n", "    word_count={}\n", "    with open(file_path,'r') as file:\n", "        for line in file:\n", "            words=line.split()\n", "            for word in words:\n", "                word=word.lower().strip('.,!?;:\"\\'')\n", "                word_count[word]=word_count.get(word,0)+1\n", "    \n", "    return word_count\n", "\n", "filepath='sample.txt'\n", "word_frequency=count_word_frequency(filepath)\n", "print(word_frequency)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Example 7: <PERSON><PERSON><PERSON>ail Address"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n", "False\n"]}], "source": ["import re\n", "\n", "# Email validation function\n", "def is_valid_email(email):\n", "    \"\"\"This function checks if the email is valid.\"\"\"\n", "    pattern = r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$'\n", "    return re.match(pattern, email) is not None\n", "\n", "# Calling the function\n", "print(is_valid_email(\"<EMAIL>\"))  # Output: True\n", "print(is_valid_email(\"invalid-email\"))  # Output: False\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}