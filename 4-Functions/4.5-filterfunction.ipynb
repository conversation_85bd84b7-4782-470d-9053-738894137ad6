{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["##### The filter() Function in Python\n", "The filter() function constructs an iterator from elements of an iterable for which a function returns true. It is used to filter out items from a list (or any other iterable) based on a condition."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["def even(num):\n", "    if num%2==0:\n", "        return True"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["even(24)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["[2, 4, 6, 8, 10, 12]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["lst=[1,2,3,4,5,6,7,8,9,10,11,12]\n", "\n", "list(filter(even,lst))\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[6, 7, 8, 9]\n"]}], "source": ["## filter with a Lambda Function\n", "numbers=[1,2,3,4,5,6,7,8,9]\n", "greater_than_five=list(filter(lambda x:x>5,numbers))\n", "print(greater_than_five)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[6, 8]\n"]}], "source": ["## Filter with a lambda function and multiple conditions\n", "numbers=[1,2,3,4,5,6,7,8,9]\n", "even_and_greater_than_five=list(filter(lambda x:x>5 and x%2==0,numbers))\n", "print(even_and_greater_than_five)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'name': '<PERSON><PERSON>', 'age': 32}, {'name': '<PERSON>', 'age': 33}]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["## Filter() to check if the age is greate than 25 in dictionaries\n", "people=[\n", "    {'name':'<PERSON><PERSON>','age':32},\n", "    {'name':'<PERSON>','age':33},\n", "    {'name':'<PERSON>','age':25}\n", "]\n", "\n", "def age_greater_than_25(person):\n", "    return person['age']>25\n", "\n", "list(filter(age_greater_than_25,people))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Conclusion\n", "The filter() function is a powerful tool for creating iterators that filter items out of an iterable based on a function. It is commonly used for data cleaning, filtering objects, and removing unwanted elements from lists. By mastering filter(), you can write more concise and efficient code for processing and manipulating collections in Python."]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}