#!/usr/bin/env python
# coding: utf-8

# #### The map() Function in Python
# The map() function applies a given function to all items in an input list (or any other iterable) and returns a map object (an iterator). This is particularly useful for transforming data in a list comprehensively.

# In[2]:


def square(x):
    return x*x

square(10)


# In[5]:


numbers=[1,2,3,4,5,6,7,8]

list(map(square,numbers))


# In[6]:


## Lambda function with map
numbers=[1,2,3,4,5,6,7,8]
list(map(lambda x:x*x,numbers))


# In[7]:


### MAp multiple iterables

numbers1=[1,2,3]
numbers2=[4,5,6]

added_numbers=list(map(lambda x,y:x+y,numbers1,numbers2))
print(added_numbers)


# In[8]:


## map() to convert a list of strings to integers
# Use map to convert strings to integers
str_numbers = ['1', '2', '3', '4', '5']
int_numbers = list(map(int, str_numbers))

print(int_numbers)  # Output: [1, 2, 3, 4, 5]


# In[9]:


words=['apple','banana','cherry']
upper_word=list(map(str.upper,words))
print(upper_word)


# In[10]:


def get_name(person):
    return person['name']

people=[
    {'name':'Krish','age':32},
    {'name':'Jack','age':33}
]
list(map(get_name,people))



# #### Conclusion
# The map() function is a powerful tool for applying transformations to iterable data structures. It can be used with regular functions, lambda functions, and even multiple iterables, providing a versatile approach to data processing in Python. By understanding and utilizing map(), you can write more efficient and readable code.

# In[ ]:




