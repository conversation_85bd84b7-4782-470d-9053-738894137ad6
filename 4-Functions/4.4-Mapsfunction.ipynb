{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### The map() Function in Python\n", "The map() function applies a given function to all items in an input list (or any other iterable) and returns a map object (an iterator). This is particularly useful for transforming data in a list comprehensively."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["100"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["def square(x):\n", "    return x*x\n", "\n", "square(10)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 4, 9, 16, 25, 36, 49, 64]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["numbers=[1,2,3,4,5,6,7,8]\n", "\n", "list(map(square,numbers))\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 4, 9, 16, 25, 36, 49, 64]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["## Lambda function with map\n", "numbers=[1,2,3,4,5,6,7,8]\n", "list(map(lambda x:x*x,numbers))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[5, 7, 9]\n"]}], "source": ["### MAp multiple iterables\n", "\n", "numbers1=[1,2,3]\n", "numbers2=[4,5,6]\n", "\n", "added_numbers=list(map(lambda x,y:x+y,numbers1,numbers2))\n", "print(added_numbers)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1, 2, 3, 4, 5]\n"]}], "source": ["## map() to convert a list of strings to integers\n", "# Use map to convert strings to integers\n", "str_numbers = ['1', '2', '3', '4', '5']\n", "int_numbers = list(map(int, str_numbers))\n", "\n", "print(int_numbers)  # Output: [1, 2, 3, 4, 5]\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['APPLE', 'BANANA', 'CHERRY']\n"]}], "source": ["words=['apple','banana','cherry']\n", "upper_word=list(map(str.upper,words))\n", "print(upper_word)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["['<PERSON><PERSON>', '<PERSON>']"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["def get_name(person):\n", "    return person['name']\n", "\n", "people=[\n", "    {'name':'<PERSON><PERSON>','age':32},\n", "    {'name':'<PERSON>','age':33}\n", "]\n", "list(map(get_name,people))\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Conclusion\n", "The map() function is a powerful tool for applying transformations to iterable data structures. It can be used with regular functions, lambda functions, and even multiple iterables, providing a versatile approach to data processing in Python. By understanding and utilizing map(), you can write more efficient and readable code."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}