{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["##### Lambda Functions in Python\n", "Lambda functions are small anonymous functions defined using the **lambda** keyword. They can have any number of arguments but only one expression. They are commonly used for short operations or as arguments to higher-order functions.\n", "\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["#Syntax\n", "lambda arguments: expression\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def addition(a,b):\n", "    return a+b"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["5"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["addition(2,3)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["11\n"]}], "source": ["addition=lambda a,b:a+b\n", "type(addition)\n", "print(addition(5,6))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["def even(num):\n", "    if num%2==0:\n", "        return True\n", "    \n", "even(24)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["even1=lambda num:num%2==0\n", "even1(12)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["39"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["def addition(x,y,z):\n", "    return x+y+z\n", "\n", "addition(12,13,14)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["39"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["addition1=lambda x,y,z:x+y+z\n", "addition1(12,13,14)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["4"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["## map()- applies a function to all items in a list\n", "numbers=[1,2,3,4,5,6]\n", "def square(number):\n", "    return number**2\n", "\n", "square(2)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 4, 9, 16, 25, 36]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["list(map(lambda x:x**2,numbers))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}