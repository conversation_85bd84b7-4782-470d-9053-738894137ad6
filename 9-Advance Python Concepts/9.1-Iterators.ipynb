{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Iterators\n", "Iterators are advanced Python concepts that allow for efficient looping and memory management. Iterators provide a way to access elements of a collection sequentially without exposing the underlying structure.\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "2\n", "3\n", "4\n", "5\n", "6\n"]}], "source": ["my_list=[1,2,3,4,5,6]\n", "for i in my_list:\n", "    print(i)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["list"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["type(my_list)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1, 2, 3, 4, 5, 6]\n"]}], "source": ["print(my_list)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'list_iterator'>\n"]}], "source": ["## Iterator\n", "iterator=iter(my_list)\n", "print(type(iterator))"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["<list_iterator at 0x27a325efb20>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["iterator"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"ename": "StopIteration", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mStopIteration\u001b[0m                             Traceback (most recent call last)", "Cell \u001b[1;32mIn[17], line 3\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m## Iterate through all the element\u001b[39;00m\n\u001b[1;32m----> 3\u001b[0m \u001b[38;5;28;43mnext\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43miterator\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[1;31mStopIteration\u001b[0m: "]}], "source": ["## Iterate through all the element\n", "\n", "next(iterator)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["iterator=iter(my_list)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are no elements in the iterator\n"]}], "source": ["\n", "\n", "try:\n", "    print(next(iterator))\n", "except StopIteration:\n", "    print(\"There are no elements in the iterator\")"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["H\n", "e\n"]}], "source": ["# String iterator\n", "my_string = \"Hello\"\n", "string_iterator = iter(my_string)\n", "\n", "print(next(string_iterator))  # Output: H\n", "print(next(string_iterator))  # Output: e"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}