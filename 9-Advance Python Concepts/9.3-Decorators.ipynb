{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### Decorators\n", "Decorators are a powerful and flexible feature in Python that allows you to modify the behavior of a function or class method. They are commonly used to add functionality to functions or methods without modifying their actual code. This lesson covers the basics of decorators, including how to create and use them."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### function copy\n", "### closures\n", "### decorators"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Welcome to the advanced python course'"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["## function copy\n", "def welcome():\n", "    return \"Welcome to the advanced python course\"\n", "\n", "welcome()"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Welcome to the advanced python course\n", "Welcome to the advanced python course\n"]}], "source": ["wel=welcome\n", "print(wel())\n", "del welcome\n", "print(wel())"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["##closures functions\n", "\n", "def main_welcome(msg):\n", "   \n", "    def sub_welcome_method():\n", "        print(\"Welcome to the advance python course\")\n", "        print(msg)\n", "        print(\"Please learn these concepts properly\")\n", "    return sub_welcome_method()"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Welcome to the advance python course\n", "Welcome everyone\n", "Please learn these concepts properly\n"]}], "source": ["main_welcome(\"Welcome everyone\")"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["def main_welcome(func):\n", "   \n", "    def sub_welcome_method():\n", "        print(\"Welcome to the advance python course\")\n", "        func(\"Welcome everyone to this tutorial\")\n", "        print(\"Please learn these concepts properly\")\n", "    return sub_welcome_method()"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Welcome to the advance python course\n", "Welcome everyone to this tutorial\n", "Please learn these concepts properly\n"]}], "source": ["main_welcome(print)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["def main_welcome(func,lst):\n", "   \n", "    def sub_welcome_method():\n", "        print(\"Welcome to the advance python course\")\n", "        print(func(lst))\n", "        print(\"Please learn these concepts properly\")\n", "    return sub_welcome_method()"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Welcome to the advance python course\n", "5\n", "Please learn these concepts properly\n"]}], "source": ["main_welcome(len,[1,2,3,4,5])"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/plain": ["6"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["len([1,2,3,4,5,6])"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["### Decorator\n", "def main_welcome(func):\n", "   \n", "    def sub_welcome_method():\n", "        print(\"Welcome to the advance python course\")\n", "        func()\n", "        print(\"Please learn these concepts properly\")\n", "    return sub_welcome_method()"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["This is an advanced python course\n"]}], "source": ["def coure_introduction():\n", "    print(\"This is an advanced python course\")\n", "\n", "coure_introduction()"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Welcome to the advance python course\n", "This is an advanced python course\n", "Please learn these concepts properly\n"]}], "source": ["main_welcome(coure_introduction)"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Welcome to the advance python course\n", "This is an advanced python course\n", "Please learn these concepts properly\n"]}], "source": ["@main_welcome\n", "def coure_introduction():\n", "    print(\"This is an advanced python course\")"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["## Decorator\n", "\n", "def my_decorator(func):\n", "    def wrapper():\n", "        print(\"Something is happening before the function is called.\")\n", "        func()\n", "        print(\"Something is happening after the function is called.\")\n", "    return wrapper"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["@my_decorator\n", "def say_hello():\n", "    print(\"Hello!\")\n", "\n"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Something is happening before the function is called.\n", "Hello!\n", "Something is happening after the function is called.\n"]}], "source": ["say_hello()"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["## Decorators WWith arguments\n", "def repeat(n):\n", "    def decorator(func):\n", "        def wrapper(*args, **kwargs):\n", "            for _ in range(n):\n", "                func(*args, **kwargs)\n", "        return wrapper\n", "    return decorator"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [], "source": ["@repeat(3)\n", "def say_hello():\n", "    print(\"Hello\")"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello\n", "Hello\n", "Hello\n"]}], "source": ["say_hello()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Conclusion\n", "Decorators are a powerful tool in Python for extending and modifying the behavior of functions and methods. They provide a clean and readable way to add functionality such as logging, timing, access control, and more without changing the original code. Understanding and using decorators effectively can significantly enhance your Python programming skills."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}